const fs = require('fs');
const path = require('path');

function getFiles(dir) {
  const files = fs.readdirSync(dir);

  console.log('files', files.length)

  files.sort(function (a, b) {
    a = Number(a.split('.')[0]) || 0
    b = Number(b.split('.')[0]) || 0

    return a - b;
  });

  fs.writeFileSync(path.resolve(__dirname, 'customer.json'), JSON.stringify(files), { flag: 'w' })
}
const directory = './customer';

getFiles(directory)

