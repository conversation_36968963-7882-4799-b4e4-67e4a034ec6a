import React from 'react';
import Link from 'next/link';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="text-center">
        <div className="mb-8">
          <h1 className="text-9xl font-bold text-gray-300">404</h1>
        </div>
        
        <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
          页面未找到
        </h2>
        
        <p className="text-lg text-gray-600 mb-8 max-w-md mx-auto">
          抱歉，您访问的页面不存在或已被移动。请检查网址是否正确，或返回首页继续浏览。
        </p>
        
        <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
          <Link
            href="/zh"
            className="inline-block bg-orange-500 text-white px-8 py-3 rounded-lg font-bold hover:bg-orange-600 transition-colors"
          >
            返回首页
          </Link>
          
          <Link
            href="/zh/aboutus"
            className="inline-block bg-gray-200 text-gray-800 px-8 py-3 rounded-lg font-bold hover:bg-gray-300 transition-colors"
          >
            关于我们
          </Link>
        </div>
        
        <div className="mt-12">
          <p className="text-sm text-gray-500 mb-4">
            或者您可以访问以下页面：
          </p>
          <div className="flex flex-wrap justify-center gap-4 text-sm">
            <Link href="/zh/calculator" className="text-orange-500 hover:text-orange-600">
              成本计算器
            </Link>
            <Link href="/zh/countries" className="text-orange-500 hover:text-orange-600">
              国家指南
            </Link>
            <Link href="/zh/articles" className="text-orange-500 hover:text-orange-600">
              文章资讯
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}