import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Image from 'next/image';
import { Locale } from '@/types';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ContactForm from '@/components/ContactForm';

interface CountryPageProps {
  params: {
    locale: Locale;
    slug: string;
  };
}

interface CountryGuide {
  id: string;
  title: string;
  country: string;
  content: string;
  bannerImage?: string;
  flagImage?: string;
  currency: string;
  capital: string;
  language: string;
  salaryPayment: string;
  seoDescription: string;
}

// 模拟国家指南数据 - 实际应该从数据库或API获取
const getCountryGuideBySlug = async (slug: string): Promise<CountryGuide | null> => {
  const guides: Record<string, CountryGuide> = {
    'Hire-In-Singapore': {
      id: 'singapore',
      title: 'Employer of Record in Singapore',
      country: 'Singapore',
      currency: 'Singapore Dollar (SGD)',
      capital: 'Singapore',
      language: 'English, Tamil, Malay, and Mandarin Chinese',
      salaryPayment: 'Monthly',
      bannerImage: 'https://blog.smartdeer.work/wp-content/uploads/2024/04/新加坡Banner.webp',
      flagImage: '/images/flags/singapore.png',
      content: `
        <h2>Basic Information</h2>
        <ul>
          <li><strong>Currency:</strong> Singapore Dollar (SGD)</li>
          <li><strong>Capital:</strong> Singapore</li>
          <li><strong>Official Language:</strong> English, Tamil, Malay, and Mandarin Chinese</li>
          <li><strong>Salary Payment:</strong> Monthly</li>
        </ul>

        <h2>Localized Benefits for Employees</h2>
        <ul>
          <li>Pension Fund (Central Provident Fund - CPF)</li>
          <li>Public Health Insurance</li>
          <li>Skill Development Levy (SDL)</li>
          <li>Commercial insurance (optional)</li>
        </ul>

        <h2>Pay & Tax</h2>
        <h3>1. Minimum Wage Requirements</h3>
        <p>There is no minimum wage requirement in Singapore. However, there is a minimum wage requirement for work visa applicants.</p>

        <h3>2. Individual Income Tax</h3>
        <p>The individual income tax ranges from 0% to 22%. Income tax is calculated according to progressive rates. Multiple additional factors may impact overall rates such as household status and the number of children.</p>

        <table class="tax-table">
          <thead>
            <tr>
              <th>Gross Annual Income</th>
              <th>Tax Rate (%)</th>
            </tr>
          </thead>
          <tbody>
            <tr><td>Up to SGD 20,000</td><td>0</td></tr>
            <tr><td>Up to SGD 30,000</td><td>2</td></tr>
            <tr><td>Up to SGD 40,000</td><td>3.5</td></tr>
            <tr><td>Up to SGD 80,000</td><td>7</td></tr>
            <tr><td>Up to SGD 120,000</td><td>11.5</td></tr>
            <tr><td>Up to SGD 160,000</td><td>15</td></tr>
            <tr><td>Up to SGD 200,000</td><td>18</td></tr>
            <tr><td>Up to SGD 240,000</td><td>19</td></tr>
            <tr><td>Up to SGD 280,000</td><td>19.5</td></tr>
            <tr><td>Up to SGD 320,000</td><td>20</td></tr>
            <tr><td>Over SGD 320,000</td><td>22</td></tr>
          </tbody>
        </table>

        <h3>3. Payroll Cost</h3>
        <p>Employer cost is generally 17.25% of employee salary:</p>
        <ul>
          <li>Pension Fund (Central Provident Fund or CPF) - 17%</li>
          <li>Skill Development Fund - 0.25%</li>
        </ul>
        <p><em>Note: 17% is the maximum contribution rate and the amount varies based on the employee's citizen status, age, and salary. The maximum salary for the calculation of this contribution is SGD 6,800.</em></p>

        <h3>4. Overtime Pay & Maximum Hours</h3>
        <p>Standard working hours are up to 9 hours per day or 44 hours a week. The standard workweek is from Monday to Friday.</p>
        <p>Overtime payment is not mandatory and is assumed included in employee salary.</p>

        <h2>Leave Policy</h2>
        <h3>1. Maternity Leave</h3>
        <p>Pregnant employees who have worked for 3 consecutive months are entitled to 16 weeks of paid leave. 4 weeks must be taken before the child's birth and 8 weeks after the birth. The remaining 4 weeks can be used as the employee wishes.</p>

        <h3>2. Paternity Leave</h3>
        <p>Employees who have worked for 3 consecutive months are entitled to 2 weeks of paid paternity leave. The 2 weeks must be taken continuously anytime within the 16 weeks of maternity leave.</p>

        <h3>3. Sick Leave</h3>
        <p>Employees are entitled to paid sick leave for up to 14 days provided they have worked for at least 3 months. The number of sick days varies depending on the length of service.</p>

        <h2>Termination</h2>
        <h3>1. Termination Requirements</h3>
        <p>Terminations in Singapore can be complex. There is no at-will termination in Singapore for employers and termination must be done for just cause.</p>

        <h3>2. Notice Period</h3>
        <p>The minimum notice period is 1 day and will be increased according to the length of employment:</p>
        <ul>
          <li>1 day if the length of service is less than 26 weeks</li>
          <li>1 week if the length of service is between 26 weeks and 2 years</li>
          <li>2 weeks if the length of service is between 2 years and 5 years</li>
          <li>4 weeks if service is longer than 5 years</li>
        </ul>

        <h3>3. Severance for Employees</h3>
        <p>In Singapore, there is no obligation to pay severance.</p>

        <h2>Statutory Time Off</h2>
        <h3>1. Paid Time Off</h3>
        <p>Full-time employees are entitled to 7 days of paid time off (PTO) a year. PTO accrues monthly 0.58 days per month. Employees are eligible for annual leave after 3 months of work.</p>

        <h3>2. Public Holidays</h3>
        <p>Singapore celebrates 10 national holidays which last 11 days in total, including New Year's Day, Chinese New Year (2 days), Good Friday, Labour Day, and others.</p>

        <h2>Onboarding</h2>
        <p>Onboarding takes 2 business days after the client signs the SOW.</p>

        <h2>Additional Information</h2>
        <h3>1. Employment Contract Details</h3>
        <p>Contracts must be in English and can be bilingual. They must be in writing and signed by both parties.</p>

        <h3>2. Probation Period</h3>
        <p>Probation periods are not mandatory. The minimum probation period is 3 months and the maximum probation period is 6 months.</p>
      `,
      seoDescription: 'Complete guide to hiring employees in Singapore. Learn about EOR services, employment laws, tax rates, benefits, and compliance requirements for Singapore.'
    },
    'Hire-In-Japan': {
      id: 'japan',
      title: 'Employer of Record in Japan',
      country: 'Japan',
      currency: 'Japanese Yen (JPY)',
      capital: 'Tokyo',
      language: 'Japanese',
      salaryPayment: 'Monthly',
      bannerImage: '/images/countries/japan-banner.jpg',
      flagImage: '/images/flags/japan.png',
      content: `
        <h2>Basic Information</h2>
        <ul>
          <li><strong>Currency:</strong> Japanese Yen (JPY)</li>
          <li><strong>Capital:</strong> Tokyo</li>
          <li><strong>Official Language:</strong> Japanese</li>
          <li><strong>Salary Payment:</strong> Monthly</li>
        </ul>

        <h2>Employment Laws & Regulations</h2>
        <p>Japan has comprehensive employment laws that protect both employers and employees. Understanding these regulations is crucial for successful business operations.</p>

        <h2>Tax Structure</h2>
        <p>Japan has a progressive income tax system with rates ranging from 5% to 45%, plus local taxes.</p>

        <h2>Social Security & Benefits</h2>
        <p>Japan's social security system includes health insurance, pension insurance, employment insurance, and workers' compensation insurance.</p>

        <h2>Working Hours & Overtime</h2>
        <p>Standard working hours are 8 hours per day and 40 hours per week. Overtime regulations are strictly enforced.</p>

        <h2>Leave Policies</h2>
        <p>Japan provides various types of leave including annual leave, sick leave, maternity/paternity leave, and special holidays.</p>
      `,
      seoDescription: 'Comprehensive guide to hiring employees in Japan. Learn about Japanese employment laws, tax structure, social security, and EOR services.'
    }
  };
  
  return guides[slug] || null;
};

export async function generateMetadata({ params }: CountryPageProps): Promise<Metadata> {
  const guide = await getCountryGuideBySlug(params.slug);
  
  if (!guide) {
    return {
      title: 'Country Guide Not Found',
    };
  }

  return {
    title: guide.title,
    description: guide.seoDescription,
    openGraph: {
      title: guide.title,
      description: guide.seoDescription,
      images: guide.bannerImage ? [guide.bannerImage] : [],
      type: 'article',
    },
    twitter: {
      card: 'summary_large_image',
      title: guide.title,
      description: guide.seoDescription,
      images: guide.bannerImage ? [guide.bannerImage] : [],
    },
  };
}

export default async function CountryPage({ params }: CountryPageProps) {
  const guide = await getCountryGuideBySlug(params.slug);
  
  if (!guide) {
    notFound();
  }

  const getContent = () => {
    const content = {
      zh: {
        basicInfo: '基本信息',
        currency: '货币',
        capital: '首都',
        language: '官方语言',
        salaryPayment: '薪资支付',
        contactTitle: '需要专业咨询？',
        contactDescription: '我们的专家团队可以为您提供详细的雇佣指南和合规建议。',
        contactButton: '联系专家'
      },
      en: {
        basicInfo: 'Basic Information',
        currency: 'Currency',
        capital: 'Capital',
        language: 'Official Language',
        salaryPayment: 'Salary Payment',
        contactTitle: 'Need Professional Consultation?',
        contactDescription: 'Our expert team can provide you with detailed employment guides and compliance advice.',
        contactButton: 'Contact Expert'
      },
      ja: {
        basicInfo: '基本情報',
        currency: '通貨',
        capital: '首都',
        language: '公用語',
        salaryPayment: '給与支払い',
        contactTitle: '専門的なコンサルテーションが必要ですか？',
        contactDescription: '私たちの専門チームが、詳細な雇用ガイドとコンプライアンスアドバイスを提供します。',
        contactButton: '専門家に相談'
      }
    };
    
    return content[params.locale] || content.zh;
  };

  const content = getContent();

  return (
    <div className="min-h-screen bg-white">
      <Header locale={params.locale} />
      
      <main className="pt-20">
        {/* Country Banner */}
        <section className="relative">
          {/* Banner Image */}
          {guide.bannerImage && (
            <div className="relative h-64 lg:h-80">
              <Image
                src={guide.bannerImage}
                alt={guide.country}
                fill
                className="object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-40" />
            </div>
          )}
          
          {/* Flag and Title */}
          <div className="relative bg-gray-50 py-8">
            <div className="container-responsive">
              <div className="flex items-center gap-6">
                {guide.flagImage && (
                  <div className="w-16 h-12 relative flex-shrink-0">
                    <Image
                      src={guide.flagImage}
                      alt={`${guide.country} flag`}
                      fill
                      className="object-cover rounded"
                    />
                  </div>
                )}
                <div>
                  <h1 className="text-3xl lg:text-4xl font-bold text-gray-900">
                    {guide.title}
                  </h1>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Content */}
        <section className="py-16">
          <div className="container-responsive">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
              {/* Main Content */}
              <div className="lg:col-span-3">
                <div 
                  className="prose prose-lg max-w-none prose-headings:text-gray-900 prose-p:text-gray-700 prose-a:text-orange-500 hover:prose-a:text-orange-600 prose-table:table-auto prose-th:bg-gray-50 prose-th:font-semibold prose-td:border prose-th:border prose-table:border-collapse"
                  dangerouslySetInnerHTML={{ __html: guide.content }}
                />
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                <div className="sticky top-24 space-y-8">
                  {/* Basic Info Card */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="text-lg font-bold text-gray-900 mb-4">
                      {content.basicInfo}
                    </h3>
                    <div className="space-y-3 text-sm">
                      <div>
                        <span className="font-medium text-gray-600">{content.currency}:</span>
                        <span className="ml-2 text-gray-900">{guide.currency}</span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-600">{content.capital}:</span>
                        <span className="ml-2 text-gray-900">{guide.capital}</span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-600">{content.language}:</span>
                        <span className="ml-2 text-gray-900">{guide.language}</span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-600">{content.salaryPayment}:</span>
                        <span className="ml-2 text-gray-900">{guide.salaryPayment}</span>
                      </div>
                    </div>
                  </div>

                  {/* Contact Form */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="text-lg font-bold text-gray-900 mb-4">
                      {content.contactTitle}
                    </h3>
                    <p className="text-gray-600 mb-6 text-sm">
                      {content.contactDescription}
                    </p>
                    <ContactForm locale={params.locale} />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer locale={params.locale} />
    </div>
  );
}
