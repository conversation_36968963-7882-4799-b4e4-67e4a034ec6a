import React from 'react';
import { Metadata } from 'next';
import Link from 'next/link';
import { Locale } from '@/types';
import { generateMetadata as generateSeoMetadata } from '@/utils/seo';

interface CountriesPageProps {
  params: Promise<{ locale: Locale }>;
}

export async function generateMetadata({ params }: CountriesPageProps): Promise<Metadata> {
  const { locale } = await params;
  
  const titles = {
    zh: '国家雇佣指南 - SmartDeer 全球人力资源服务',
    en: 'Global Hiring Guide - SmartDeer Global HR Services',
    ja: '国家雇佣指南 - SmartDeer グローバル人事サービス'
  };
  
  const descriptions = {
    zh: '了解全球150+国家的雇佣法规、税务政策、社保制度等详细信息，为您的全球化扩张提供专业指导。',
    en: 'Learn about employment regulations, tax policies, social security systems and more in 150+ countries worldwide to guide your global expansion.',
    ja: '世界150+国の雇用法規、税務政策、社会保障制度などの詳細情報を理解し、グローバル展開に専門的なガイダンスを提供します。'
  };

  return generateSeoMetadata({
    title: titles[locale] || titles.zh,
    description: descriptions[locale] || descriptions.zh
  }, locale);
}

export default async function CountriesPage({ params }: CountriesPageProps) {
  const { locale } = await params;

  const getContent = () => {
    const content = {
      zh: {
        title: '国家雇佣指南',
        subtitle: '全球150+国家雇佣法规详解',
        description: '深入了解各国雇佣法规、税务政策、社保制度，为您的全球化业务提供专业指导。',
        regions: {
          title: '按地区浏览',
          items: [
            {
              name: '亚太地区',
              description: '中国、日本、新加坡、澳大利亚等',
              countries: ['中国', '日本', '新加坡', '澳大利亚', '韩国', '印度'],
              image: '/images/regions/asia-pacific.jpg'
            },
            {
              name: '北美地区',
              description: '美国、加拿大、墨西哥等',
              countries: ['美国', '加拿大', '墨西哥'],
              image: '/images/regions/north-america.jpg'
            },
            {
              name: '欧洲地区',
              description: '英国、德国、法国、荷兰等',
              countries: ['英国', '德国', '法国', '荷兰', '意大利', '西班牙'],
              image: '/images/regions/europe.jpg'
            },
            {
              name: '其他地区',
              description: '中东、非洲、南美等',
              countries: ['阿联酋', '南非', '巴西', '阿根廷'],
              image: '/images/regions/others.jpg'
            }
          ]
        },
        popular: {
          title: '热门国家',
          countries: [
            {
              name: '新加坡',
              flag: '🇸🇬',
              description: '亚洲金融中心，营商环境优越',
              highlights: ['低税率', '高效政府', '多元文化'],
              slug: 'Hire-In-Singapore'
            },
            {
              name: '美国',
              flag: '🇺🇸',
              description: '全球最大经济体，完善的法律体系',
              highlights: ['at-will雇佣', '联邦与州法律并存', '丰富的人才资源'],
              slug: 'Hire-In-United-States'
            },
            {
              name: '日本',
              flag: '🇯🇵',
              description: '亚洲发达经济体，技术创新领先',
              highlights: ['终身雇佣制', '严格的劳动法', '高素质人才'],
              slug: 'Hire-In-Japan'
            },
            {
              name: '英国',
              flag: '🇬🇧',
              description: '欧洲金融中心，成熟的商业环境',
              highlights: ['灵活的雇佣法', '强大的金融体系', '英语环境'],
              slug: 'Hire-In-Great-Britain'
            },
            {
              name: '德国',
              flag: '🇩🇪',
              description: '欧洲经济引擎，制造业发达',
              highlights: ['严格的劳动保护', '高技能人才', '稳定的经济'],
              slug: 'Hire-In-Germany'
            },
            {
              name: '澳大利亚',
              flag: '🇦🇺',
              description: '亚太地区重要市场，资源丰富',
              highlights: ['公平工作法', '最低工资保障', '多元化社会'],
              slug: 'Hire-In-Australia'
            },
            {
              name: '韩国',
              flag: '🇰🇷',
              description: '东亚科技强国，创新活跃',
              highlights: ['科技产业发达', '严格的劳动标准', '高教育水平'],
              slug: 'Hire-In-South-Korea'
            },
            {
              name: '泰国',
              flag: '🇹🇭',
              description: '东南亚制造业中心，成本优势明显',
              highlights: ['制造业发达', '劳动成本较低', '地理位置优越'],
              slug: 'Hire-In-Thailand'
            },
            {
              name: '香港',
              flag: '🇭🇰',
              description: '国际金融中心，自由贸易港',
              highlights: ['低税率', '法律体系完善', '国际化程度高'],
              slug: 'Hire-In-Hong-Kong'
            },
            {
              name: '台湾',
              flag: '🇹🇼',
              description: '科技产业发达，人才素质高',
              highlights: ['科技制造业', '高素质人才', '创新能力强'],
              slug: 'Hire-In-Taiwan'
            }
          ]
        },
        allCountries: {
          title: '所有国家指南',
          description: '浏览我们覆盖的150+个国家和地区的详细雇佣指南',
          countries: [
            { name: '阿尔巴尼亚', slug: 'Hire-employees-with-Smartdeer-in-Albania' },
            { name: '阿根廷', slug: 'Hire-employees-with-Smartdeer-in-Argentina' },
            { name: '澳大利亚', slug: 'Hire-In-Australia' },
            { name: '玻利维亚', slug: 'Hire-employees-with-Smartdeer-in-Bolivia' },
            { name: '波斯尼亚和黑塞哥维那', slug: 'Hire-employees-with-Smartdeer-in-Bosnia and Herzegovina' },
            { name: '巴西', slug: 'Hire-employees-with-Smartdeer-in-Brazil' },
            { name: '喀麦隆', slug: 'Hire-employees-with-Smartdeer-in-Cameroon' },
            { name: '加拿大', slug: 'Hire-employees-with-Smartdeer-in-Canada' },
            { name: '智利', slug: 'Hire-employees-with-Smartdeer-in-Chile' },
            { name: '哥伦比亚', slug: 'Hire-employees-with-Smartdeer-in-Colombia' },
            { name: '哥斯达黎加', slug: 'Hire-employees-with-Smartdeer-in-Costa Rica' },
            { name: '克罗地亚', slug: 'Hire-employees-with-Smartdeer-in-Croatia' },
            { name: '捷克共和国', slug: 'Hire-employees-with-Smartdeer-in-Czech Republic' },
            { name: '厄瓜多尔', slug: 'Hire-employees-with-Smartdeer-in-Ecuador' },
            { name: '埃及', slug: 'Hire-employees-with-Smartdeer-in-Egypt' },
            { name: '法国', slug: 'Hire-employees-with-Smartdeer-in-France' },
            { name: '德国', slug: 'Hire-In-Germany' },
            { name: '加纳', slug: 'Hire-employees-with-Smartdeer-in-Ghana' },
            { name: '英国', slug: 'Hire-In-Great-Britain' },
            { name: '希腊', slug: 'Hire-employees-with-Smartdeer-in-Greek' },
            { name: '危地马拉', slug: 'Hire-employees-with-Smartdeer-in-Guatemala' },
            { name: '洪都拉斯', slug: 'Hire-employees-with-Smartdeer-in-Honduras' },
            { name: '香港', slug: 'Hire-In-Hong-Kong' },
            { name: '印度尼西亚', slug: 'Hire-employees-with-Smartdeer-in-Indonesia' },
            { name: '意大利', slug: 'Hire-employees-with-Smartdeer-in-Italy' },
            { name: '日本', slug: 'Hire-In-Japan' },
            { name: '肯尼亚', slug: 'Hire-employees-with-Smartdeer-in-Kenya' },
            { name: '马来西亚', slug: 'Hire-employees-with-Smartdeer-in-Malaysia' },
            { name: '马耳他', slug: 'Hire-employees-with-Smartdeer-in-Malta' },
            { name: '墨西哥', slug: 'Hire-employees-with-Smartdeer-in-Mexico' },
            { name: '黑山', slug: 'Hire-employees-with-Smartdeer-in-Montenegro' },
            { name: '摩洛哥', slug: 'Hire-employees-with-Smartdeer-in-Morocco' },
            { name: '荷兰', slug: 'Hire-employees-with-Smartdeer-in-Netherlands' },
            { name: '尼日利亚', slug: 'Hire-employees-with-Smartdeer-in-Nigeria' },
            { name: '北马其顿', slug: 'Hire-employees-with-Smartdeer-in-North Macedonia' },
            { name: '巴基斯坦', slug: 'Hire-employees-with-Smartdeer-in-Pakistan' },
            { name: '巴拿马', slug: 'Hire-employees-with-Smartdeer-in-Panama' },
            { name: '巴拉圭', slug: 'Hire-employees-with-Smartdeer-in-Paraguay' },
            { name: '秘鲁', slug: 'Hire-employees-with-Smartdeer-in-Peru' },
            { name: '菲律宾', slug: 'Hire-employees-with-Smartdeer-in-Philippin' },
            { name: '波兰', slug: 'Hire-employees-with-Smartdeer-in-Poland' },
            { name: '葡萄牙', slug: 'Hire-employees-with-Smartdeer-in-Portugal' },
            { name: '罗马尼亚', slug: 'Hire-employees-with-Smartdeer-in-Romania' },
            { name: '卢旺达', slug: 'Hire-employees-with-Smartdeer-in-Rwanda' },
            { name: '塞尔维亚', slug: 'Hire-employees-with-Smartdeer-in-Serbia' },
            { name: '新加坡', slug: 'Hire-In-Singapore' },
            { name: '斯洛伐克', slug: 'Hire-employees-with-Smartdeer-in-Slovakia' },
            { name: '斯洛文尼亚', slug: 'Hire-employees-with-Smartdeer-in-Slovenia' },
            { name: '韩国', slug: 'Hire-In-South-Korea' },
            { name: '西班牙', slug: 'Hire-employees-with-Smartdeer-in-Spain' },
            { name: '瑞典', slug: 'Hire-employees-with-Smartdeer-in-Sweden' },
            { name: '台湾', slug: 'Hire-In-Taiwan' },
            { name: '泰国', slug: 'Hire-In-Thailand' },
            { name: '突尼斯', slug: 'Hire-employees-with-Smartdeer-in-Tunisia' },
            { name: '土耳其', slug: 'Hire-employees-with-Smartdeer-in-Turkey' },
            { name: '阿联酋', slug: 'Hire-employees-with-Smartdeer-in-UAE' },
            { name: '乌干达', slug: 'Hire-employees-with-Smartdeer-in-Uganda' },
            { name: '美国', slug: 'Hire-In-United-States' },
            { name: '乌拉圭', slug: 'Hire-employees-with-Smartdeer-in-Uruguay' }
          ]
        },
        features: {
          title: '我们提供的信息',
          items: [
            {
              icon: '📋',
              title: '雇佣法规',
              description: '详细的劳动法律法规解读'
            },
            {
              icon: '💰',
              title: '税务政策',
              description: '个人所得税和企业税务指南'
            },
            {
              icon: '🏥',
              title: '社保制度',
              description: '社会保险和福利制度说明'
            },
            {
              icon: '📄',
              title: '合同模板',
              description: '标准雇佣合同模板下载'
            },
            {
              icon: '⚖️',
              title: '合规要求',
              description: '当地合规要求和注意事项'
            },
            {
              icon: '📊',
              title: '成本分析',
              description: '雇佣成本详细分析报告'
            }
          ]
        }
      },
      en: {
        title: 'Global Hiring Guide',
        subtitle: 'Employment regulations in 150+ countries worldwide',
        description: 'Gain deep insights into employment regulations, tax policies, and social security systems in various countries to guide your global business.',
        regions: {
          title: 'Browse by Region',
          items: [
            {
              name: 'Asia Pacific',
              description: 'China, Japan, Singapore, Australia, etc.',
              countries: ['China', 'Japan', 'Singapore', 'Australia', 'South Korea', 'India'],
              image: '/images/regions/asia-pacific.jpg'
            },
            {
              name: 'North America',
              description: 'United States, Canada, Mexico, etc.',
              countries: ['United States', 'Canada', 'Mexico'],
              image: '/images/regions/north-america.jpg'
            },
            {
              name: 'Europe',
              description: 'United Kingdom, Germany, France, Netherlands, etc.',
              countries: ['United Kingdom', 'Germany', 'France', 'Netherlands', 'Italy', 'Spain'],
              image: '/images/regions/europe.jpg'
            },
            {
              name: 'Other Regions',
              description: 'Middle East, Africa, South America, etc.',
              countries: ['UAE', 'South Africa', 'Brazil', 'Argentina'],
              image: '/images/regions/others.jpg'
            }
          ]
        },
        popular: {
          title: 'Popular Countries',
          countries: [
            {
              name: 'United States',
              flag: '🇺🇸',
              description: 'World\'s largest economy with comprehensive legal system',
              highlights: ['At-will employment', 'Federal and state laws', 'Rich talent pool']
            },
            {
              name: 'Singapore',
              flag: '🇸🇬',
              description: 'Asian financial hub with excellent business environment',
              highlights: ['Low tax rates', 'Efficient government', 'Multicultural']
            },
            {
              name: 'United Kingdom',
              flag: '🇬🇧',
              description: 'European financial center with mature business environment',
              highlights: ['Flexible employment law', 'Strong financial system', 'English-speaking']
            },
            {
              name: 'Germany',
              flag: '🇩🇪',
              description: 'European economic engine with advanced manufacturing',
              highlights: ['Strict labor protection', 'Highly skilled workforce', 'Stable economy']
            }
          ]
        },
        features: {
          title: 'Information We Provide',
          items: [
            {
              icon: '📋',
              title: 'Employment Regulations',
              description: 'Detailed interpretation of labor laws and regulations'
            },
            {
              icon: '💰',
              title: 'Tax Policies',
              description: 'Personal income tax and corporate tax guidelines'
            },
            {
              icon: '🏥',
              title: 'Social Security',
              description: 'Social insurance and benefit system explanations'
            },
            {
              icon: '📄',
              title: 'Contract Templates',
              description: 'Standard employment contract template downloads'
            },
            {
              icon: '⚖️',
              title: 'Compliance Requirements',
              description: 'Local compliance requirements and considerations'
            },
            {
              icon: '📊',
              title: 'Cost Analysis',
              description: 'Detailed employment cost analysis reports'
            }
          ]
        }
      },
      ja: {
        title: '国家雇佣指南',
        subtitle: '世界150+国の雇用法規詳細解説',
        description: '各国の雇用法規、税務政策、社会保障制度を深く理解し、グローバルビジネスに専門的なガイダンスを提供します。',
        regions: {
          title: '地域別閲覧',
          items: [
            {
              name: 'アジア太平洋',
              description: '中国、日本、シンガポール、オーストラリアなど',
              countries: ['中国', '日本', 'シンガポール', 'オーストラリア', '韓国', 'インド'],
              image: '/images/regions/asia-pacific.jpg'
            },
            {
              name: '北米',
              description: 'アメリカ、カナダ、メキシコなど',
              countries: ['アメリカ', 'カナダ', 'メキシコ'],
              image: '/images/regions/north-america.jpg'
            },
            {
              name: 'ヨーロッパ',
              description: 'イギリス、ドイツ、フランス、オランダなど',
              countries: ['イギリス', 'ドイツ', 'フランス', 'オランダ', 'イタリア', 'スペイン'],
              image: '/images/regions/europe.jpg'
            },
            {
              name: 'その他の地域',
              description: '中東、アフリカ、南米など',
              countries: ['UAE', '南アフリカ', 'ブラジル', 'アルゼンチン'],
              image: '/images/regions/others.jpg'
            }
          ]
        },
        popular: {
          title: '人気の国',
          countries: [
            {
              name: 'アメリカ',
              flag: '🇺🇸',
              description: '世界最大の経済体、完備された法律体系',
              highlights: ['随意雇用', '連邦法と州法の併存', '豊富な人材資源']
            },
            {
              name: 'シンガポール',
              flag: '🇸🇬',
              description: 'アジアの金融センター、優れたビジネス環境',
              highlights: ['低税率', '効率的な政府', '多文化']
            },
            {
              name: 'イギリス',
              flag: '🇬🇧',
              description: 'ヨーロッパの金融センター、成熟したビジネス環境',
              highlights: ['柔軟な雇用法', '強力な金融システム', '英語環境']
            },
            {
              name: 'ドイツ',
              flag: '🇩🇪',
              description: 'ヨーロッパの経済エンジン、製造業が発達',
              highlights: ['厳格な労働保護', '高技能人材', '安定した経済']
            }
          ]
        },
        features: {
          title: '提供する情報',
          items: [
            {
              icon: '📋',
              title: '雇用法規',
              description: '詳細な労働法律法規の解読'
            },
            {
              icon: '💰',
              title: '税務政策',
              description: '個人所得税と企業税務ガイド'
            },
            {
              icon: '🏥',
              title: '社会保障制度',
              description: '社会保険と福利制度の説明'
            },
            {
              icon: '📄',
              title: '契約テンプレート',
              description: '標準雇用契約テンプレートダウンロード'
            },
            {
              icon: '⚖️',
              title: 'コンプライアンス要件',
              description: '現地コンプライアンス要件と注意事項'
            },
            {
              icon: '📊',
              title: 'コスト分析',
              description: '雇用コスト詳細分析レポート'
            }
          ]
        }
      }
    };
    
    return content[locale] || content.zh;
  };

  const content = getContent();

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-orange-50 py-16 lg:py-24">
        <div className="container-responsive text-center">
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            {content.title}
          </h1>
          <p className="text-xl text-gray-600 mb-6">
            {content.subtitle}
          </p>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto">
            {content.description}
          </p>
        </div>
      </section>

      {/* Regions Section */}
      <section className="py-16 lg:py-24">
        <div className="container-responsive">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 text-center mb-16">
            {content.regions.title}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {content.regions.items.map((region, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                <div className="h-48 bg-gradient-to-br from-blue-100 to-orange-100 flex items-center justify-center">
                  <span className="text-gray-500 text-lg">{region.name}</span>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {region.name}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {region.description}
                  </p>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {region.countries.map((country, countryIndex) => (
                      <span
                        key={countryIndex}
                        className="px-3 py-1 bg-orange-100 text-orange-800 text-sm rounded-full"
                      >
                        {country}
                      </span>
                    ))}
                  </div>
                  <Link
                    href={`/${locale}/countries/${region.name.toLowerCase().replace(/\s+/g, '-')}`}
                    className="text-orange-500 font-medium hover:text-orange-600 transition-colors"
                  >
                    {locale === 'zh' ? '查看详情' : locale === 'en' ? 'View Details' : '詳細を見る'} →
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Popular Countries */}
      <section className="py-16 lg:py-24 bg-gray-50">
        <div className="container-responsive">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 text-center mb-16">
            {content.popular.title}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {content.popular.countries.map((country, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg p-6 text-center hover:shadow-xl transition-shadow">
                <div className="text-4xl mb-4">{country.flag}</div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {country.name}
                </h3>
                <p className="text-gray-600 mb-4 text-sm">
                  {country.description}
                </p>
                <div className="space-y-2">
                  {country.highlights.map((highlight, highlightIndex) => (
                    <div key={highlightIndex} className="text-xs bg-blue-50 text-blue-800 px-2 py-1 rounded">
                      {highlight}
                    </div>
                  ))}
                </div>
                <Link
                  href={`/${locale}/countries/${country.name.toLowerCase().replace(/\s+/g, '-')}`}
                  className="mt-4 inline-block text-orange-500 font-medium hover:text-orange-600 transition-colors"
                >
                  {locale === 'zh' ? '了解更多' : locale === 'en' ? 'Learn More' : 'もっと見る'} →
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 lg:py-24">
        <div className="container-responsive">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 text-center mb-16">
            {content.features.title}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {content.features.items.map((feature, index) => (
              <div key={index} className="text-center space-y-4">
                <div className="text-4xl">{feature.icon}</div>
                <h3 className="text-xl font-bold text-gray-900">
                  {feature.title}
                </h3>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 lg:py-24 bg-orange-50">
        <div className="container-responsive text-center">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
            {locale === 'zh' ? '需要专业咨询？' : locale === 'en' ? 'Need Professional Consultation?' : '専門的な相談が必要ですか？'}
          </h2>
          <p className="text-lg text-gray-700 mb-8 max-w-2xl mx-auto">
            {locale === 'zh' 
              ? '我们的专家团队可以为您提供详细的国家雇佣指导和定制化解决方案。'
              : locale === 'en'
              ? 'Our expert team can provide you with detailed country employment guidance and customized solutions.'
              : '専門チームが詳細な国家雇用ガイダンスとカスタマイズされたソリューションを提供します。'
            }
          </p>
          <button className="bg-orange-500 text-white px-8 py-3 rounded-lg font-bold hover:bg-orange-600 transition-colors">
            {locale === 'zh' ? '联系专家' : locale === 'en' ? 'Contact Expert' : '専門家に相談'}
          </button>
        </div>
      </section>
    </div>
  );
}