import React from 'react';
import { notFound } from 'next/navigation';
import { Locale } from '@/types';
import LocaleLayoutClient from './LocaleLayoutClient';

const LOCALES = ['zh', 'en', 'ja'];

interface LocaleLayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}

export default async function LocaleLayout({ children, params }: LocaleLayoutProps) {
  const { locale: localeParam } = await params;
  
  // 验证语言参数
  if (!LOCALES.includes(localeParam)) {
    notFound();
  }

  const locale = localeParam as Locale;

  return (
    <LocaleLayoutClient locale={locale}>
      {children}
    </LocaleLayoutClient>
  );
}