'use client';

import React, { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ContactForm from '@/components/ContactForm';
import { Locale } from '@/types';

interface LocaleLayoutClientProps {
  children: React.ReactNode;
  locale: Locale;
}

export default function LocaleLayoutClient({ children, locale }: LocaleLayoutClientProps) {
  const [showContactForm, setShowContactForm] = useState(false);

  return (
    <div className="min-h-screen flex flex-col">
      <Header 
        locale={locale} 
        onShowContactForm={() => setShowContactForm(true)}
      />
      
      <main className="flex-1">
        {children}
      </main>
      
      <Footer 
        locale={locale}
        onShowContactForm={() => setShowContactForm(true)}
      />
      
      {showContactForm && (
        <ContactForm
          locale={locale}
          onClose={() => setShowContactForm(false)}
        />
      )}
    </div>
  );
}