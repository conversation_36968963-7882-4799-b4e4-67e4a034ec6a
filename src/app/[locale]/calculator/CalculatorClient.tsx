'use client';

import React, { useState } from 'react';
import { Locale } from '@/types';

interface CalculatorClientProps {
  locale: Locale;
}

interface CalculatorData {
  country: string;
  province: string;
  city: string;
  employType: string;
  salary: number;
  currency: string;
}

interface LocationOption {
  key: string;
  areaCode?: string;
  areaNameI18n: string;
  province?: string;
}

interface EmployTypeOption {
  key: string;
  sectionNameI18n: string;
}

interface CalculatorResult {
  grossSalary: number;
  employerCosts: {
    socialSecurity: number;
    healthInsurance: number;
    unemploymentInsurance: number;
    otherBenefits: number;
  };
  totalEmployerCost: number;
}

export default function CalculatorClient({ locale }: CalculatorClientProps) {
  const [formData, setFormData] = useState<CalculatorData>({
    country: '',
    province: '',
    city: '',
    employType: '',
    salary: 0,
    currency: ''
  });
  const [result, setResult] = useState<CalculatorResult | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);

  // Location and employment type options
  const [provinceList, setProvinceList] = useState<LocationOption[]>([]);
  const [allCityList, setAllCityList] = useState<LocationOption[]>([]);
  const [cityList, setCityList] = useState<LocationOption[]>([]);
  const [employTypeList, setEmployTypeList] = useState<EmployTypeOption[]>([]);
  const [errorMessage, setErrorMessage] = useState('');

  const getContent = () => {
    const content = {
      zh: {
        title: '雇主成本计算器',
        form: {
          country: '选择国家/地区',
          province: '选择省份/州',
          city: '选择城市',
          employType: '员工类型',
          salary: '年薪',
          calculate: '计算雇主成本',
          calculating: '计算中...'
        },
        countries: [
          { value: 'US', label: '美国' },
          { value: 'CN', label: '中国' },
          { value: 'JP', label: '日本' },
          { value: 'SG', label: '新加坡' },
          { value: 'UK', label: '英国' },
          { value: 'DE', label: '德国' },
          { value: 'AU', label: '澳大利亚' },
          { value: 'CA', label: '加拿大' }
        ],
        results: {
          title: '成本计算结果',
          grossSalary: '基本薪资',
          employerCosts: '雇主额外成本',
          socialSecurity: '社会保险',
          healthInsurance: '医疗保险',
          unemploymentInsurance: '失业保险',
          otherBenefits: '其他福利',
          totalEmployerCost: '雇主总成本',
          note: '* 以上计算结果仅供参考，实际成本可能因具体情况而异。如需准确报价，请联系我们的专业顾问。'
        },
        contact: {
          title: '需要专业咨询？',
          description: '我们的专家团队可以为您提供详细的成本分析和定制化解决方案。',
          button: '联系专家'
        }
      },
      en: {
        title: 'Employer Cost Calculator',
        form: {
          country: 'Select Country',
          province: 'Select Province/State',
          city: 'Select City',
          employType: 'Employee Type',
          salary: 'Annual Salary',
          calculate: 'Calculate Cost',
          calculating: 'Calculating...'
        },
        countries: [
          { value: 'US', label: 'United States' },
          { value: 'CN', label: 'China' },
          { value: 'JP', label: 'Japan' },
          { value: 'SG', label: 'Singapore' },
          { value: 'UK', label: 'United Kingdom' },
          { value: 'DE', label: 'Germany' },
          { value: 'AU', label: 'Australia' },
          { value: 'CA', label: 'Canada' }
        ],
        results: {
          title: 'Cost Calculation Results',
          grossSalary: 'Gross Salary',
          employerCosts: 'Additional Employer Costs',
          socialSecurity: 'Social Security',
          healthInsurance: 'Health Insurance',
          unemploymentInsurance: 'Unemployment Insurance',
          otherBenefits: 'Other Benefits',
          totalEmployerCost: 'Total Employer Cost',
          note: '* The above calculation results are for reference only. Actual costs may vary depending on specific circumstances. For accurate quotes, please contact our professional consultants.'
        },
        contact: {
          title: 'Need Professional Consultation?',
          description: 'Our expert team can provide you with detailed cost analysis and customized solutions.',
          button: 'Contact Expert'
        }
      },
      ja: {
        title: '採用コスト計算ツール',
        form: {
          country: '国を選択',
          province: '都道府県を選択',
          city: '都市を選択',
          employType: '従業員タイプ',
          salary: '年収',
          calculate: 'コスト計算',
          calculating: '計算中...'
        },
        countries: [
          { value: 'US', label: 'アメリカ' },
          { value: 'CN', label: '中国' },
          { value: 'JP', label: '日本' },
          { value: 'SG', label: 'シンガポール' },
          { value: 'UK', label: 'イギリス' },
          { value: 'DE', label: 'ドイツ' },
          { value: 'AU', label: 'オーストラリア' },
          { value: 'CA', label: 'カナダ' }
        ],
        results: {
          title: 'コスト計算結果',
          grossSalary: '基本給',
          employerCosts: '雇用主追加コスト',
          socialSecurity: '社会保険',
          healthInsurance: '健康保険',
          unemploymentInsurance: '失業保険',
          otherBenefits: 'その他の福利厚生',
          totalEmployerCost: '雇用主総コスト',
          note: '* 上記の計算結果は参考用です。実際のコストは具体的な状況により異なる場合があります。正確な見積もりについては、専門コンサルタントにお問い合わせください。'
        },
        contact: {
          title: '専門的な相談が必要ですか？',
          description: '専門チームが詳細なコスト分析とカスタマイズされたソリューションを提供します。',
          button: '専門家に相談'
        }
      }
    };
    
    return content[locale] || content.zh;
  };

  const content = getContent();

  // 处理国家变化
  const handleCountryChange = async (countryCode: string) => {
    setFormData(prev => ({
      ...prev,
      country: countryCode,
      province: '',
      city: '',
      employType: '',
      currency: ''
    }));

    // 清空下级选项
    setProvinceList([]);
    setAllCityList([]);
    setCityList([]);
    setEmployTypeList([]);
    setErrorMessage('');

    // 获取该国家的配置信息
    await fetchCountryConfig(countryCode);
  };

  // 处理省份变化
  const handleProvinceChange = (provinceCode: string) => {
    setFormData(prev => ({
      ...prev,
      province: provinceCode,
      city: ''
    }));

    // 根据省份筛选城市
    const filteredCities = allCityList.filter(city => city.province === provinceCode);
    setCityList(filteredCities);
  };

  // 获取国家配置信息
  const fetchCountryConfig = async (countryCode: string) => {
    try {
      // 这里应该调用实际的API，现在使用模拟数据
      const mockData = getMockCountryConfig(countryCode);

      if (!mockData.enabled) {
        setErrorMessage(mockData.errorMessage || '当前国家暂不支持成本计算');
        return;
      }

      setProvinceList(mockData.provinces || []);
      setAllCityList(mockData.cities || []);
      setEmployTypeList(mockData.employTypes || []);
      setFormData(prev => ({
        ...prev,
        currency: mockData.currency || 'USD'
      }));
    } catch (error) {
      console.error('Failed to fetch country config:', error);
      setErrorMessage('获取国家配置失败，请稍后重试');
    }
  };

  // 模拟国家配置数据
  const getMockCountryConfig = (countryCode: string) => {
    const configs: Record<string, {
      enabled: boolean;
      currency?: string;
      provinces?: LocationOption[];
      cities?: LocationOption[];
      employTypes?: EmployTypeOption[];
      errorMessage?: string;
    }> = {
      'US': {
        enabled: true,
        currency: 'USD',
        provinces: [
          { key: 'CA', areaNameI18n: '加利福尼亚州' },
          { key: 'NY', areaNameI18n: '纽约州' },
          { key: 'TX', areaNameI18n: '德克萨斯州' }
        ],
        cities: [
          { key: 'SF', areaNameI18n: '旧金山', province: 'CA' },
          { key: 'LA', areaNameI18n: '洛杉矶', province: 'CA' },
          { key: 'NYC', areaNameI18n: '纽约市', province: 'NY' }
        ],
        employTypes: [
          { key: 'FULL_TIME', sectionNameI18n: '全职员工' },
          { key: 'CONTRACTOR', sectionNameI18n: '承包商' }
        ]
      },
      'CN': {
        enabled: true,
        currency: 'CNY',
        provinces: [
          { key: 'BJ', areaNameI18n: '北京市' },
          { key: 'SH', areaNameI18n: '上海市' },
          { key: 'GD', areaNameI18n: '广东省' }
        ],
        cities: [
          { key: 'BJ_CITY', areaNameI18n: '北京市', province: 'BJ' },
          { key: 'SH_CITY', areaNameI18n: '上海市', province: 'SH' },
          { key: 'SZ', areaNameI18n: '深圳市', province: 'GD' },
          { key: 'GZ', areaNameI18n: '广州市', province: 'GD' }
        ],
        employTypes: [
          { key: 'FULL_TIME', sectionNameI18n: '全职员工' },
          { key: 'PART_TIME', sectionNameI18n: '兼职员工' }
        ]
      }
    };

    return configs[countryCode] || {
      enabled: false,
      errorMessage: '当前国家暂不支持成本计算，请联系我们获取详细报价'
    };
  };

  const handleInputChange = (field: keyof CalculatorData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 计算成本
  const calculateCosts = async () => {
    if (!formData.country || !formData.salary) {
      setErrorMessage('请填写完整信息');
      return;
    }

    setIsCalculating(true);
    setErrorMessage('');

    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1500));

      // 获取该国家的税率配置
      const baseRate = getMockTaxRates(formData.country);

      // 计算各项成本
      const mockResult: CalculatorResult = {
        grossSalary: formData.salary,
        employerCosts: {
          socialSecurity: formData.salary * baseRate.socialSecurity,
          healthInsurance: formData.salary * baseRate.healthInsurance,
          unemploymentInsurance: formData.salary * baseRate.unemploymentInsurance,
          otherBenefits: formData.salary * baseRate.otherBenefits
        },
        totalEmployerCost: formData.salary * (1 + baseRate.socialSecurity + baseRate.healthInsurance + baseRate.unemploymentInsurance + baseRate.otherBenefits)
      };

      setResult(mockResult);
    } catch (error) {
      console.error('Failed to calculate costs:', error);
      setErrorMessage('计算失败，请稍后重试');
    } finally {
      setIsCalculating(false);
    }
  };

  // 获取模拟税率数据
  const getMockTaxRates = (countryCode: string) => {
    const rates: Record<string, {
      socialSecurity: number;
      healthInsurance: number;
      unemploymentInsurance: number;
      otherBenefits: number;
    }> = {
      'US': {
        socialSecurity: 0.062,
        healthInsurance: 0.08,
        unemploymentInsurance: 0.006,
        otherBenefits: 0.05
      },
      'CN': {
        socialSecurity: 0.16,
        healthInsurance: 0.06,
        unemploymentInsurance: 0.005,
        otherBenefits: 0.03
      },
      'JP': {
        socialSecurity: 0.155,
        healthInsurance: 0.05,
        unemploymentInsurance: 0.003,
        otherBenefits: 0.04
      }
    };

    return rates[countryCode] || {
      socialSecurity: 0.12,
      healthInsurance: 0.07,
      unemploymentInsurance: 0.005,
      otherBenefits: 0.05
    };
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(locale === 'zh' ? 'zh-CN' : locale === 'ja' ? 'ja-JP' : 'en-US', {
      style: 'currency',
      currency: formData.currency || 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="calculator-page">
      <header style={{ background: '#fff6ec' }}>
        <div className="header-banner relative w-[1204px] h-[620px] mx-auto">
          <div
            className="absolute inset-0 bg-no-repeat bg-cover"
            style={{
              backgroundImage: 'url("/images/calculator/banner.png")',
              backgroundSize: '1204px 590px',
              backgroundPosition: '0px'
            }}
          />
          <div className="header-banner-text absolute left-0 w-[641px]">
            <h1 className="header-title text-[40px] font-bold mt-[190px] text-gray-900">
              {locale === 'zh' ? '确定全球雇主成本' :
               locale === 'en' ? 'Determine Global Employer Costs' :
               '世界の雇用主コストを決定'}
            </h1>
            <p className="header-desc text-lg leading-[30px] tracking-[1px] font-light text-gray-700 mt-4">
              {locale === 'zh' ? '准备好在另一个国家/地区雇用员工了吗？根据团队成员位置即时汇总雇佣成本，为您的企业做出最佳的招聘举措。' :
               locale === 'en' ? 'Ready to hire employees in another country? Instantly summarize employment costs based on team member locations to make the best hiring decisions for your business.' :
               '他の国で従業員を雇用する準備はできていますか？チームメンバーの場所に基づいて雇用コストを即座に集計し、ビジネスに最適な採用決定を行います。'}
            </p>
          </div>
          <div className="header-form absolute top-[80px] right-0 w-[405px] bg-white border border-gray-200 rounded-[22px] shadow-lg p-[40px_30px]">
            <h2 className="calculator-title text-center text-[#170F49] text-xl font-semibold mb-4">
              {content.title}
            </h2>
            <p className="calculator-desc text-[#6F6C90] text-sm mb-6">
              {locale === 'zh' ? '选择您要雇佣员工的国家和地区，填入员工年度薪资，即可查询在当地的雇主成本。' :
               locale === 'en' ? 'Select the country and region where you want to hire employees, enter the employee annual salary, and you can query the local employer costs.' :
               '従業員を雇用したい国と地域を選択し、従業員の年収を入力すると、現地の雇用主コストを照会できます。'}
            </p>
            <div className="form-body space-y-4">
              {/* Country Selection */}
              <div>
                <select
                  value={formData.country}
                  onChange={(e) => handleCountryChange(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm"
                >
                  <option value="">{content.form.country}</option>
                  {content.countries.map((country) => (
                    <option key={country.value} value={country.value}>
                      {country.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Province Selection */}
              {provinceList.length > 0 && (
                <div>
                  <select
                    value={formData.province}
                    onChange={(e) => handleProvinceChange(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm"
                  >
                    <option value="">{content.form.province}</option>
                    {provinceList.map((province) => (
                      <option key={province.key} value={province.key}>
                        {province.areaNameI18n}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* City Selection */}
              {cityList.length > 0 && (
                <div>
                  <select
                    value={formData.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm"
                  >
                    <option value="">{content.form.city}</option>
                    {cityList.map((city) => (
                      <option key={city.key} value={city.key}>
                        {city.areaNameI18n}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* Employee Type Selection */}
              {employTypeList.length > 0 && (
                <div>
                  <select
                    value={formData.employType}
                    onChange={(e) => handleInputChange('employType', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm"
                  >
                    <option value="">{content.form.employType}</option>
                    {employTypeList.map((type) => (
                      <option key={type.key} value={type.key}>
                        {type.sectionNameI18n}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* Salary Input */}
              <div>
                <input
                  type="number"
                  value={formData.salary || ''}
                  onChange={(e) => handleInputChange('salary', Number(e.target.value))}
                  placeholder={locale === 'zh' ? '请输入年薪' : locale === 'en' ? 'Enter annual salary' : '年収を入力してください'}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm"
                />
              </div>
              {errorMessage && (
                <div className="text-red-600 text-sm mt-2">
                  {errorMessage}
                </div>
              )}
              <button
                onClick={calculateCosts}
                disabled={
                  !formData.country ||
                  !formData.salary ||
                  isCalculating ||
                  errorMessage !== '' ||
                  (cityList.length > 0 && !formData.city)
                }
                className="calculator-submit w-full h-[56px] text-white rounded-[50px] font-bold disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                style={{
                  background: 'linear-gradient(259deg, #F54A25 -42%, #FFAB71 98%)'
                }}
              >
                {isCalculating ? content.form.calculating : content.form.calculate}
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="calculator-container w-[1204px] mx-auto py-16">
        <h2 className="result-title text-[32px] font-bold text-center text-gray-900 mb-4">
          {locale === 'zh' ? '确定全球员工成本' :
           locale === 'en' ? 'Determine employee costs across the globe' :
           '世界中の従業員コストを決定'}
        </h2>
        <p className="result-description text-lg text-center text-gray-600 mb-12 max-w-4xl mx-auto">
          {locale === 'zh' ? 'SmartDeer 可以帮助您决定雇用下一位员工的最佳地点，并全面了解您可以期望支付的税费、费用、福利等。' :
           locale === 'en' ? 'SmartDeer can help you decide the best place to hire your next employee and get a comprehensive understanding of the taxes, fees, benefits, and more you can expect to pay.' :
           'SmartDeerは、次の従業員を雇用するのに最適な場所を決定し、支払うことが期待される税金、手数料、福利厚生などの包括的な理解を得るのに役立ちます。'}
        </p>

        {result ? (
          <div className="bg-white rounded-lg shadow-lg max-w-4xl mx-auto overflow-hidden">
            <div className="px-8 py-6 bg-gray-50 border-b">
              <h3 className="text-2xl font-bold text-gray-900 text-center">
                {content.results.title}
              </h3>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <tbody>
                  <tr className="border-b border-gray-200">
                    <td className="px-8 py-4 text-gray-700 font-medium bg-gray-50">
                      {content.results.grossSalary}
                    </td>
                    <td className="px-8 py-4 text-right font-bold text-lg">
                      {formatCurrency(result.grossSalary)}
                    </td>
                  </tr>

                  <tr className="border-b border-gray-200">
                    <td className="px-8 py-4 text-gray-700 font-medium bg-gray-50">
                      {content.results.socialSecurity}
                    </td>
                    <td className="px-8 py-4 text-right">
                      {formatCurrency(result.employerCosts.socialSecurity)}
                    </td>
                  </tr>

                  <tr className="border-b border-gray-200">
                    <td className="px-8 py-4 text-gray-700 font-medium bg-gray-50">
                      {content.results.healthInsurance}
                    </td>
                    <td className="px-8 py-4 text-right">
                      {formatCurrency(result.employerCosts.healthInsurance)}
                    </td>
                  </tr>

                  <tr className="border-b border-gray-200">
                    <td className="px-8 py-4 text-gray-700 font-medium bg-gray-50">
                      {content.results.unemploymentInsurance}
                    </td>
                    <td className="px-8 py-4 text-right">
                      {formatCurrency(result.employerCosts.unemploymentInsurance)}
                    </td>
                  </tr>

                  <tr className="border-b border-gray-200">
                    <td className="px-8 py-4 text-gray-700 font-medium bg-gray-50">
                      {content.results.otherBenefits}
                    </td>
                    <td className="px-8 py-4 text-right">
                      {formatCurrency(result.employerCosts.otherBenefits)}
                    </td>
                  </tr>

                  <tr className="bg-orange-50 border-t-2 border-orange-500">
                    <td className="px-8 py-4 text-gray-900 font-bold text-lg">
                      {content.results.totalEmployerCost}
                    </td>
                    <td className="px-8 py-4 text-right font-bold text-xl text-orange-500">
                      {formatCurrency(result.totalEmployerCost)}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div className="px-8 py-4 bg-yellow-50 border-t border-yellow-200">
              <p className="text-sm text-yellow-800 text-center">
                {content.results.note}
              </p>
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
              <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            </div>
            <p className="text-gray-500">
              {locale === 'zh' ? '请填写上方表单开始计算' : locale === 'en' ? 'Please fill out the form above to start calculating' : 'フォームに記入して計算を開始してください'}
            </p>
          </div>
        )}

        <div className="mt-16 bg-white rounded-lg shadow-lg p-8 text-center max-w-4xl mx-auto">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            {content.contact.title}
          </h3>
          <p className="text-gray-700 mb-6 max-w-2xl mx-auto">
            {content.contact.description}
          </p>
          <button
            className="calculator-contact w-[200px] h-[50px] text-white rounded-[25px] font-bold transition-colors"
            style={{
              background: 'linear-gradient(259deg, #F54A25 -42%, #FFAB71 98%)'
            }}
          >
            {content.contact.button}
          </button>
        </div>
      </div>
    </div>
  );
}
