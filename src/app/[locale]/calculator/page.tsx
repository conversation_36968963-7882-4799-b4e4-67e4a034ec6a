import React from 'react';
import { Metadata } from 'next';
import { Locale } from '@/types';
import { generateMetadata as generateSeoMetadata } from '@/utils/seo';
import CalculatorClient from './CalculatorClient';

interface CalculatorPageProps {
  params: Promise<{ locale: Locale }>;
}

export async function generateMetadata({ params }: CalculatorPageProps): Promise<Metadata> {
  const { locale } = await params;
  
  const titles = {
    zh: '雇主成本计算器 - SmartDeer 全球人力资源服务',
    en: 'Employer Cost Calculator - SmartDeer Global HR Services',
    ja: '採用コスト計算ツール - SmartDeer グローバル人事サービス'
  };
  
  const descriptions = {
    zh: '使用 SmartDeer 雇主成本计算器，快速计算全球各国的雇佣成本，包括薪资、税费、社保等详细费用明细。',
    en: 'Use SmartDeer Employer Cost Calculator to quickly calculate employment costs in countries worldwide, including salary, taxes, social security and detailed cost breakdown.',
    ja: 'SmartDeer 採用コスト計算ツールを使用して、世界各国の雇用コストを迅速に計算し、給与、税金、社会保障などの詳細な費用内訳を確認できます。'
  };

  return generateSeoMetadata({
    title: titles[locale] || titles.zh,
    description: descriptions[locale] || descriptions.zh
  }, locale);
}

export default async function CalculatorPage({ params }: CalculatorPageProps) {
  const { locale } = await params;

  return <CalculatorClient locale={locale} />;
}