import React from 'react';
import { Metadata } from 'next';
import Image from 'next/image';
import { Locale } from '@/types';
import { generateMetadata as generateSeoMetadata } from '@/utils/seo';

interface AboutUsPageProps {
  params: Promise<{ locale: Locale }>;
}

export async function generateMetadata({ params }: AboutUsPageProps): Promise<Metadata> {
  const { locale } = await params;
  
  const titles = {
    zh: '关于我们 - SmartDeer 全球人力资源服务',
    en: 'About Us - SmartDeer Global HR Services',
    ja: '会社概要 - SmartDeer グローバル人事サービス'
  };
  
  const descriptions = {
    zh: '了解 SmartDeer 的发展历程、企业文化和团队。我们致力于为全球企业提供专业的人力资源服务解决方案。',
    en: 'Learn about SmartDeer\'s journey, corporate culture, and team. We are committed to providing professional HR service solutions for global enterprises.',
    ja: 'SmartDeer の歩み、企業文化、チームについてご紹介します。グローバル企業向けの専門的な人事サービスソリューションの提供に取り組んでいます。'
  };

  return generateSeoMetadata({
    title: titles[locale] || titles.zh,
    description: descriptions[locale] || descriptions.zh
  }, locale);
}

export default async function AboutUsPage({ params }: AboutUsPageProps) {
  const { locale } = await params;

  const getContent = () => {
    const content = {
      zh: {
        title: '关于我们',
        subtitle: 'Your Global HR Solutions Partner',
        companyProfile: {
          title: '公司简介',
          sections: [
            {
              title: '公司背景',
              content: 'SmartDeer 是一站式"人力服务+SaaS"平台，专注于全球招聘和雇佣解决方案。由信托桥资本孵化，微光创投、WeWork、Hash Global 等领投，SmartDeer 帮助企业超越地理界限，快速招聘全球人才，管理整个雇佣生命周期，包括入职、离职、薪资、税务和福利。我们的平台确保每一步的合规性和效率，简化全球团队管理。SmartDeer 已获得 ISO 27001 认证，确保全面保护客户数据和隐私，提供可信赖的服务。',
              image: '/images/aboutus/background.jpeg'
            },
            {
              title: '全球影响力',
              content: 'SmartDeer 双总部设在香港和新加坡，为我们提供战略性全球优势，同时确保符合当地数据存储和处理法规。除了在美国、英国、阿联酋、沙特阿拉伯、澳大利亚、日本、韩国、泰国、马来西亚、印度尼西亚、菲律宾、越南、中国大陆、墨西哥和巴西等地区的子公司和分支机构外，我们的服务通过自营网络和合作伙伴覆盖全球150多个国家和地区。',
              image: '/images/aboutus/globalization.jpeg'
            },
            {
              title: '我们的团队',
              content: 'SmartDeer 在十多个国家拥有150多名员工，提供多语言服务，重点关注中文和英文，并在各种当地语言方面具有强大的能力，以满足区域需求。我们团队在当地法律法规方面的深厚专业知识使我们能够提供专业的本地化服务和全球支持。',
              image: '/images/index/eor.webp'
            },
            {
              title: '服务范围',
              content: '我们为人才招聘、全球合规雇佣、签证处理、薪资管理、福利和税务管理提供全面的HR解决方案。SmartDeer 强大的全球HR SaaS系统使企业能够轻松管理全球HR运营的复杂性，简化流程并降低合规风险。我们的平台允许公司在全球招聘人才，管理入职和离职，跟踪假期，无缝处理薪资和税务，帮助企业在全球取得成功。',
              image: '/images/aboutus/scope.jpeg'
            }
          ]
        },
        profession: {
          title: '专业优势',
          items: [
            {
              icon: '/images/aboutus/earth.svg',
              title: '全球协作',
              description: '我们的团队遍布美国、英国、阿联酋、沙特阿拉伯、澳大利亚、新加坡、日本、韩国、泰国、马来西亚、印度尼西亚、菲律宾、越南、香港等地。'
            },
            {
              icon: '/images/aboutus/team.svg',
              title: '专业团队',
              description: '我们的成员来自世界各地，在顶级公司拥有超过10年的HR经验。'
            },
            {
              icon: '/images/aboutus/cover.svg',
              title: '全面服务覆盖',
              description: '我们的服务包括全球人才招聘、EOR、全球承包商、全球人力服务专业外包和咨询。'
            }
          ]
        },
        globalOffice: {
          title: '全球办公室',
          offices: [
            {
              name: '香港',
              image: '/images/aboutus/hongkong.webp',
              address: '香港中环德辅道中141号中国保险集团大厦7楼705-706室'
            },
            {
              name: '新加坡',
              image: '/images/aboutus/singapore.webp',
              address: '3 Fraser Street, #5-25 Duo Tower, Singapore (189352)'
            },
            {
              name: '美国',
              image: '/images/aboutus/california.jpeg',
              address: '15025 PROCTOR AVAVE CITY OF INDUSTRY, Y, CA CA 91746'
            },
            {
              name: '英国',
              image: '/images/aboutus/uk.jpeg',
              address: '69 Aberdeen Avenue, Cambridge, England, CB2 8DL'
            },
            {
              name: '澳大利亚',
              image: '/images/aboutus/australia.jpeg',
              address: '135 KING STREET, SYDNEY, NSW 2000'
            },
            {
              name: '阿联酋',
              image: '/images/aboutus/aue.jpeg',
              address: 'Office328,BlockB,Business Village, Deira, Dubai, UAE'
            },
            {
              name: '日本',
              image: '/images/aboutus/japan.jpeg',
              address: '神奈川県横浜市中区山下町98GSハイム山下町4階403号室'
            },
            {
              name: '韩国',
              image: '/images/aboutus/korea.jpeg',
              address: '서울특별시 중랑구 동일로825，2층 205호 (중화동)'
            },
            {
              name: '泰国',
              image: '/images/aboutus/thailand.jpeg',
              address: '11 / 152-153 Room No. GDC 101, 1st Floor, Moo 5, Kookong District, Lam Luka District, Pathum Thani Province'
            },
            {
              name: '马来西亚',
              image: '/images/aboutus/malaysia.jpeg',
              address: '332F-­2, HARMONY SQUARE, JALAN PERAK 11600 JELUTONG PULAU PINANG MALAYSIA'
            },
            {
              name: '印度尼西亚',
              image: '/images/aboutus/indonesia.jpeg',
              address: 'Gedung Wirausaha Lantai 1 Unit 104, Jalan HR Rasuna Said Kav. C-5,Desa/Kelurahan Karet, Kec. Setiabudi, Kota Adm. Jakarta Selatan, ProvinsiDKI Jakarta'
            },
            {
              name: '菲律宾',
              image: '/images/aboutus/philippines.jpeg',
              address: 'UNIT 25D 2ND FLOOR ZETA II BLDG.191 SALCEDO ST., SAN LORENZO,CITY OF MAKATI,FOURTH DIRECT, NATIONAL CAPITAL REGION (NCR), 1223'
            },
            {
              name: '越南',
              image: '/images/aboutus/vietnam.jpeg',
              address: 'Room(s):28 ,Level 14, Saigon Centre Tower 1, No. 65 Le Loi Street, Ben Nghe Ward, District 1, Ho Chi Minh City,Vietnam'
            },
            {
              name: '北京',
              image: '/images/aboutus/beijing.webp',
              address: '北京市东城区王府井wework王府国际中心4F'
            },
            {
              name: '上海',
              image: '/images/aboutus/shanghai.webp',
              address: '上海市普陀区曹阳路535号汇融大厦12楼'
            },
            {
              name: '深圳',
              image: '/images/aboutus/shenzhen.webp',
              address: '深圳前海深港现代服务业合作区南山街道临海大道59号海运中心港航大厦0701-D021'
            },
            {
              name: '成都',
              image: '/images/aboutus/chengdu.webp',
              address: '四川省成都市成华区航天路5号丽晶港大厦6F-K0063'
            },
            {
              name: '杭州',
              image: '/images/aboutus/hangzhou.webp',
              address: '浙江省杭州市余杭区前街街道龙泉路8-1号2幢205室'
            }
          ]
        },
        contact: {
          title: '联系我们',
          email: '<EMAIL>',
          description: '如果您有任何问题或需要了解更多信息，请随时与我们联系。'
        }
      },
      en: {
        title: 'About Us',
        subtitle: 'Your Global HR Solutions Partner',
        companyProfile: {
          title: 'Company Profile',
          sections: [
            {
              title: 'Company Background',
              content: 'SmartDeer is a one-stop "HR service + SaaS" platform focused on global recruitment and employment solutions. Incubated by Trustbridge Partners, with investments led by Welight Capital, WeWork, and Hash Global. SmartDeer helps enterprises transcend geographical boundaries, quickly recruit global talent, and manage the entire employment lifecycle, including onboarding, offboarding, payroll, taxes, and benefits.',
              image: '/images/aboutus/background.jpeg'
            },
            {
              title: 'Global Impact',
              content: 'SmartDeer is dual-headquartered in Hong Kong and Singapore, providing us with a strategic global advantage while ensuring compliance with local data storage and processing regulations. In addition to subsidiaries and branches in regions like the United States, the United Kingdom, the UAE, Saudi Arabia, Australia, Japan, South Korea, Thailand, Malaysia, Indonesia, the Philippines, Vietnam, Mainland China, Mexico and Brazil, our services span over 150 countries and regions worldwide.',
              image: '/images/aboutus/globalization.jpeg'
            },
            {
              title: 'Our Team',
              content: 'SmartDeer has over 150 employees across more than ten countries, providing multilingual services with a focus on Chinese and English, and strong capabilities in various local languages to meet regional needs. Our team\'s deep expertise in local laws and regulations enables us to provide professional localized services and global support.',
              image: '/images/index/eor.webp'
            },
            {
              title: 'Service Scope',
              content: 'We provide comprehensive HR solutions for talent recruitment, global compliant employment, visa processing, payroll management, benefits and tax management. SmartDeer\'s powerful global HR SaaS system enables enterprises to easily manage the complexities of global HR operations, streamline processes and reduce compliance risks.',
              image: '/images/aboutus/scope.jpeg'
            }
          ]
        },
        profession: {
          title: 'Professional Advantages',
          items: [
            {
              icon: '/images/aboutus/earth.svg',
              title: 'Global Collaboration',
              description: 'Our team spans across the United States, United Kingdom, UAE, Saudi Arabia, Australia, Singapore, Japan, South Korea, Thailand, Malaysia, Indonesia, Philippines, Vietnam, Hong Kong and more.'
            },
            {
              icon: '/images/aboutus/team.svg',
              title: 'Professional Team',
              description: 'Our members come from around the world with over 10 years of HR experience at top companies.'
            },
            {
              icon: '/images/aboutus/cover.svg',
              title: 'Comprehensive Service Coverage',
              description: 'Our services include global talent recruitment, EOR, global contractors, global HR professional outsourcing and consulting.'
            }
          ]
        },
        globalOffice: {
          title: 'Global Offices',
          offices: [
            {
              name: 'Hong Kong',
              image: '/images/aboutus/hongkong.webp',
              address: 'Room 705-706, 7/F, China Insurance Group Building, 141 Des Voeux Road Central, Central, Hong Kong'
            },
            {
              name: 'Singapore',
              image: '/images/aboutus/singapore.webp',
              address: '3 Fraser Street, #5-25 Duo Tower, Singapore (189352)'
            },
            {
              name: 'United States',
              image: '/images/aboutus/california.jpeg',
              address: '15025 PROCTOR AVAVE CITY OF INDUSTRY, Y, CA CA 91746'
            },
            {
              name: 'United Kingdom',
              image: '/images/aboutus/uk.jpeg',
              address: '69 Aberdeen Avenue, Cambridge, England, CB2 8DL'
            },
            {
              name: 'Australia',
              image: '/images/aboutus/australia.jpeg',
              address: '135 KING STREET, SYDNEY, NSW 2000'
            },
            {
              name: 'UAE',
              image: '/images/aboutus/aue.jpeg',
              address: 'Office328,BlockB,Business Village, Deira, Dubai, UAE'
            },
            {
              name: 'Japan',
              image: '/images/aboutus/japan.jpeg',
              address: '神奈川県横浜市中区山下町98GSハイム山下町4階403号室'
            },
            {
              name: 'South Korea',
              image: '/images/aboutus/korea.jpeg',
              address: '서울특별시 중랑구 동일로825，2층 205호 (중화동)'
            },
            {
              name: 'Thailand',
              image: '/images/aboutus/thailand.jpeg',
              address: '11 / 152-153 Room No. GDC 101, 1st Floor, Moo 5, Kookong District, Lam Luka District, Pathum Thani Province'
            },
            {
              name: 'Malaysia',
              image: '/images/aboutus/malaysia.jpeg',
              address: '332F-­2, HARMONY SQUARE, JALAN PERAK 11600 JELUTONG PULAU PINANG MALAYSIA'
            },
            {
              name: 'Indonesia',
              image: '/images/aboutus/indonesia.jpeg',
              address: 'Gedung Wirausaha Lantai 1 Unit 104, Jalan HR Rasuna Said Kav. C-5,Desa/Kelurahan Karet, Kec. Setiabudi, Kota Adm. Jakarta Selatan, ProvinsiDKI Jakarta'
            },
            {
              name: 'Philippines',
              image: '/images/aboutus/philippines.jpeg',
              address: 'UNIT 25D 2ND FLOOR ZETA II BLDG.191 SALCEDO ST., SAN LORENZO,CITY OF MAKATI,FOURTH DIRECT, NATIONAL CAPITAL REGION (NCR), 1223'
            },
            {
              name: 'Vietnam',
              image: '/images/aboutus/vietnam.jpeg',
              address: 'Room(s):28 ,Level 14, Saigon Centre Tower 1, No. 65 Le Loi Street, Ben Nghe Ward, District 1, Ho Chi Minh City,Vietnam'
            },
            {
              name: 'Beijing',
              image: '/images/aboutus/beijing.webp',
              address: 'WeWork Wangfujing International Center 4F, Dongcheng District, Beijing'
            },
            {
              name: 'Shanghai',
              image: '/images/aboutus/shanghai.webp',
              address: '12F, Huirong Building, 535 Caoyang Road, Putuo District, Shanghai'
            },
            {
              name: 'Shenzhen',
              image: '/images/aboutus/shenzhen.webp',
              address: 'Room 0701-D021, Shipping Center Building, 59 Linhai Avenue, Nanshan Street, Qianhai Shenzhen-Hong Kong Modern Service Industry Cooperation Zone, Shenzhen'
            },
            {
              name: 'Chengdu',
              image: '/images/aboutus/chengdu.webp',
              address: 'Room K0063, 6F, Lijing Port Building, 5 Hangtian Road, Chenghua District, Chengdu, Sichuan Province'
            },
            {
              name: 'Hangzhou',
              image: '/images/aboutus/hangzhou.webp',
              address: 'Room 205, Building 2, 8-1 Longquan Road, Qianjie Street, Yuhang District, Hangzhou, Zhejiang Province'
            }
          ]
        },
        contact: {
          title: 'Contact Us',
          email: '<EMAIL>',
          description: 'If you have any questions or need more information, please feel free to contact us.'
        }
      },
      ja: {
        title: '会社概要',
        subtitle: 'グローバル人事ソリューションパートナー',
        companyProfile: {
          title: '会社プロフィール',
          sections: [
            {
              title: '会社背景',
              content: 'SmartDeerは、グローバル採用・雇用ソリューションに特化したワンストップ「人事サービス+SaaS」プラットフォームです。信託橋資本によってインキュベートされ、微光創投、WeWork、Hash Globalなどが主導投資しています。SmartDeerは企業が地理的境界を超え、迅速にグローバル人材を採用し、入社、退社、給与、税務、福利厚生を含む雇用ライフサイクル全体を管理することを支援します。',
              image: '/images/aboutus/background.jpeg'
            },
            {
              title: 'グローバルインパクト',
              content: 'SmartDeerは香港とシンガポールにデュアル本社を置き、戦略的なグローバル優位性を提供すると同時に、現地のデータ保存・処理規制への準拠を確保しています。米国、英国、UAE、サウジアラビア、オーストラリア、日本、韓国、タイ、マレーシア、インドネシア、フィリピン、ベトナム、中国本土、メキシコ、ブラジルなどの地域の子会社・支店に加え、自営ネットワークとパートナーを通じて世界150以上の国・地域にサービスを提供しています。',
              image: '/images/aboutus/globalization.jpeg'
            },
            {
              title: '私たちのチーム',
              content: 'SmartDeerは10以上の国に150名以上の従業員を擁し、中国語と英語を重点とした多言語サービスを提供し、地域ニーズに対応するための様々な現地言語での強力な能力を持っています。現地の法律法規に関するチームの深い専門知識により、プロフェッショナルな現地化サービスとグローバルサポートを提供することができます。',
              image: '/images/index/eor.webp'
            },
            {
              title: 'サービス範囲',
              content: '人材採用、グローバルコンプライアント雇用、ビザ処理、給与管理、福利厚生・税務管理のための包括的なHRソリューションを提供しています。SmartDeerの強力なグローバルHR SaaSシステムにより、企業はグローバルHR運営の複雑さを簡単に管理し、プロセスを合理化し、コンプライアンスリスクを削減することができます。',
              image: '/images/aboutus/scope.jpeg'
            }
          ]
        },
        profession: {
          title: 'プロフェッショナル優位性',
          items: [
            {
              icon: '/images/aboutus/earth.svg',
              title: 'グローバルコラボレーション',
              description: '私たちのチームは米国、英国、UAE、サウジアラビア、オーストラリア、シンガポール、日本、韓国、タイ、マレーシア、インドネシア、フィリピン、ベトナム、香港などに広がっています。'
            },
            {
              icon: '/images/aboutus/team.svg',
              title: 'プロフェッショナルチーム',
              description: '私たちのメンバーは世界各地から集まり、トップ企業で10年以上のHR経験を持っています。'
            },
            {
              icon: '/images/aboutus/cover.svg',
              title: '包括的サービスカバレッジ',
              description: '私たちのサービスには、グローバル人材採用、EOR、グローバル請負業者、グローバル人事プロフェッショナルアウトソーシング、コンサルティングが含まれます。'
            }
          ]
        },
        globalOffice: {
          title: 'グローバルオフィス',
          offices: [
            {
              name: '香港',
              image: '/images/aboutus/hongkong.webp',
              address: '香港中環德輔道中141號中國保險集團大廈7樓705-706室'
            },
            {
              name: 'シンガポール',
              image: '/images/aboutus/singapore.webp',
              address: '3 Fraser Street, #5-25 Duo Tower, Singapore (189352)'
            },
            {
              name: 'アメリカ',
              image: '/images/aboutus/california.jpeg',
              address: '15025 PROCTOR AVAVE CITY OF INDUSTRY, Y, CA CA 91746'
            },
            {
              name: 'イギリス',
              image: '/images/aboutus/uk.jpeg',
              address: '69 Aberdeen Avenue, Cambridge, England, CB2 8DL'
            },
            {
              name: 'オーストラリア',
              image: '/images/aboutus/australia.jpeg',
              address: '135 KING STREET, SYDNEY, NSW 2000'
            },
            {
              name: 'UAE',
              image: '/images/aboutus/aue.jpeg',
              address: 'Office328,BlockB,Business Village, Deira, Dubai, UAE'
            },
            {
              name: '日本',
              image: '/images/aboutus/japan.jpeg',
              address: '神奈川県横浜市中区山下町98GSハイム山下町4階403号室'
            },
            {
              name: '韓国',
              image: '/images/aboutus/korea.jpeg',
              address: '서울특별시 중랑구 동일로825，2층 205호 (중화동)'
            },
            {
              name: 'タイ',
              image: '/images/aboutus/thailand.jpeg',
              address: '11 / 152-153 Room No. GDC 101, 1st Floor, Moo 5, Kookong District, Lam Luka District, Pathum Thani Province'
            },
            {
              name: 'マレーシア',
              image: '/images/aboutus/malaysia.jpeg',
              address: '332F-­2, HARMONY SQUARE, JALAN PERAK 11600 JELUTONG PULAU PINANG MALAYSIA'
            },
            {
              name: 'インドネシア',
              image: '/images/aboutus/indonesia.jpeg',
              address: 'Gedung Wirausaha Lantai 1 Unit 104, Jalan HR Rasuna Said Kav. C-5,Desa/Kelurahan Karet, Kec. Setiabudi, Kota Adm. Jakarta Selatan, ProvinsiDKI Jakarta'
            },
            {
              name: 'フィリピン',
              image: '/images/aboutus/philippines.jpeg',
              address: 'UNIT 25D 2ND FLOOR ZETA II BLDG.191 SALCEDO ST., SAN LORENZO,CITY OF MAKATI,FOURTH DIRECT, NATIONAL CAPITAL REGION (NCR), 1223'
            },
            {
              name: 'ベトナム',
              image: '/images/aboutus/vietnam.jpeg',
              address: 'Room(s):28 ,Level 14, Saigon Centre Tower 1, No. 65 Le Loi Street, Ben Nghe Ward, District 1, Ho Chi Minh City,Vietnam'
            },
            {
              name: '北京',
              image: '/images/aboutus/beijing.webp',
              address: '北京市東城区王府井wework王府国際中心4F'
            },
            {
              name: '上海',
              image: '/images/aboutus/shanghai.webp',
              address: '上海市普陀区曹阳路535号汇融大厦12楼'
            },
            {
              name: '深圳',
              image: '/images/aboutus/shenzhen.webp',
              address: '深圳前海深港現代服務業合作区南山街道臨海大道59号海運中心港航大厦0701-D021'
            },
            {
              name: '成都',
              image: '/images/aboutus/chengdu.webp',
              address: '四川省成都市成華区航天路5号麗晶港大厦6F-K0063'
            },
            {
              name: '杭州',
              image: '/images/aboutus/hangzhou.webp',
              address: '浙江省杭州市余杭区前街街道龍泉路8-1号2幢205室'
            }
          ]
        },
        contact: {
          title: 'お問い合わせ',
          email: '<EMAIL>',
          description: 'ご質問やより詳しい情報が必要な場合は、お気軽にお問い合わせください。'
        }
      }
    };
    
    return content[locale] || content.zh;
  };

  const content = getContent();

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-orange-50 py-16 lg:py-24">
        <div className="container-responsive text-center">
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            {content.title}
          </h1>
          <p className="text-xl text-gray-600">
            {content.subtitle}
          </p>
        </div>
      </section>

      {/* Company Profile Section */}
      <section className="py-16 lg:py-24">
        <div className="container-responsive">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              {content.companyProfile.title}
            </h2>
          </div>

          <div className="space-y-24">
            {content.companyProfile.sections.map((section: { title: string; content: string; image: string }, index: number) => (
              <div key={index} className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${index % 2 === 1 ? 'lg:flex-row-reverse' : ''}`}>
                <div className={index % 2 === 1 ? 'lg:order-2' : ''}>
                  <h3 className="text-2xl font-bold text-gray-900 mb-6">
                    {section.title}
                  </h3>
                  <p className="text-lg text-gray-700 leading-relaxed">
                    {section.content}
                  </p>
                </div>
                <div className={`relative h-64 lg:h-80 ${index % 2 === 1 ? 'lg:order-1' : ''}`}>
                  <Image
                    src={section.image}
                    alt={section.title}
                    fill
                    className="object-cover rounded-lg shadow-lg"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>



      {/* Professional Advantages Section */}
      <section className="py-16 lg:py-24">
        <div className="container-responsive">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              {content.profession.title}
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {content.profession.items.map((item: { icon: string; title: string; description: string }, index: number) => (
              <div key={index} className="text-center bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition-shadow">
                <div className="w-16 h-16 mx-auto mb-6">
                  <Image
                    src={item.icon}
                    alt={item.title}
                    width={64}
                    height={64}
                    className="w-full h-full object-contain"
                  />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  {item.title}
                </h3>
                <p className="text-gray-700 leading-relaxed">
                  {item.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Global Offices Section */}
      <section className="py-16 lg:py-24 bg-gray-50">
        <div className="container-responsive">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              {content.globalOffice.title}
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {content.globalOffice.offices.map((office: { name: string; image: string; address: string }, index: number) => (
              <div key={index} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                <div className="relative h-48">
                  <Image
                    src={office.image}
                    alt={office.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">
                    {office.name}
                  </h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {office.address}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 lg:py-24">
        <div className="container-responsive text-center">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
            {content.contact.title}
          </h2>
          <p className="text-lg text-gray-700 mb-8">
            {content.contact.description}
          </p>
          <div className="space-y-4">
            <a
              href={`mailto:${content.contact.email}`}
              className="inline-block text-xl text-orange-500 hover:text-orange-600 transition-colors font-medium"
            >
              {content.contact.email}
            </a>
          </div>
        </div>
      </section>
    </div>
  );
}