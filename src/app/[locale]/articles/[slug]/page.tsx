import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Image from 'next/image';
import { Locale } from '@/types';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ContactForm from '@/components/ContactForm';

interface ArticlePageProps {
  params: {
    locale: Locale;
    slug: string;
  };
}

interface Article {
  id: string;
  title: string;
  content: string;
  image?: string;
  publishDate: string;
  author: string;
  category: string;
  tags: string[];
  seoDescription: string;
}

// 模拟文章数据 - 实际应该从数据库或API获取
const getArticleBySlug = async (slug: string): Promise<Article | null> => {
  const articles: Record<string, Article> = {
    'Why-Saudi-Arabia-is-a-great-opportunity-for-China-to-go-overseas': {
      id: 'cG9zdDoxNjQ4',
      title: '沙特为什么是中国出海的很大的机会呢',
      content: `
        <p>沙特为什么是中国出海的很大的机会呢？明白这三点，企业出海才能先人一步。</p>
        
        <h2>第一，沙特的产油大户和王室贵族的转变</h2>
        <p>沙特的那些产油大户和王室贵族们不再是你想象中的躺平爱玩儿了，他们现在也希望在事业上有所成功，完成本国建设的美好愿景，外加清洁能源的大潮加剧了石油大户们摆脱石油依赖，发展多元经济的决心。</p>
        
        <h2>第二，中国影响力的提升</h2>
        <p>中国科技和工业的进步，一带一路策略的推进，以及2023年促成沙特和伊朗的外交和解，中国的影响力越来越大，虽然沙特资本的投资重心在逐渐的展示出亲近中国的势头，对中国公司和创业者来讲是一个非常利好的机会。</p>
        
        <h2>第三，沙特的招商反投策略</h2>
        <p>沙特国家把招商引资改成了招商反投，沙特不缺钱，所以他们用资本和市场来换技术，这种玩法大大减轻了国内出海的企业负担，但是出海同时需要小心闭坑，不论是在员工雇佣方面，还是当地法律法规方面，都需要有足够多的经验。</p>
        
        <h2>SmartDeer的专业支持</h2>
        <p>我们SmartDeer为全球150多个国家和地区的企业打交道，对于企业出海需要警惕的问题有足够多的解决办法，不仅提供专业全面的HRO服务，同时帮助企业避免触碰当地法律、宗教文化问题等，最大程度为企业避坑，节约上千万运营成本。</p>
        
        <p><strong>创业伙伴们，如果你的业务对沙特有价值，那么沙特将是你第一选择。</strong></p>
      `,
      image: 'https://blog.smartdeer.work/wp-content/uploads/2024/08/WechatIMG1014.png',
      publishDate: '2024-08-15',
      author: 'SmartDeer Team',
      category: '出海指南',
      tags: ['沙特阿拉伯', '企业出海', '中东市场', '投资机会'],
      seoDescription: '沙特为什么是中国出海的很大的机会呢？明白这三点，企业出海才能先人一步。第一，沙特的那些产油大户和王室贵族们不再是你想象中的躺平爱玩儿了...'
    },
    'Singapore_an_important_springboard_for_Chinese_companies_to_go_global': {
      id: 'cG9zdDoxNzI1',
      title: '新加坡，中国企业出海的重要跳板',
      content: `
        <p>新加坡作为东南亚的金融中心和贸易枢纽，为中国企业出海提供了独特的优势和机遇。</p>
        
        <h2>地理位置优势</h2>
        <p>新加坡位于东南亚的心脏地带，是连接东西方的重要桥梁。其优越的地理位置使其成为企业进入东南亚市场的理想跳板。</p>
        
        <h2>完善的法律体系</h2>
        <p>新加坡拥有完善的法律体系和透明的商业环境，为企业提供了稳定可靠的营商环境。</p>
        
        <h2>金融服务优势</h2>
        <p>作为亚洲重要的金融中心，新加坡提供了完善的金融服务体系，便于企业进行资金管理和投融资活动。</p>
        
        <h2>人才优势</h2>
        <p>新加坡汇聚了来自世界各地的优秀人才，多元化的人才结构为企业发展提供了强有力的支撑。</p>
      `,
      image: 'https://blog.smartdeer.work/wp-content/uploads/2025/01/新加坡，中国企业出海的重要跳板.png',
      publishDate: '2025-01-10',
      author: 'SmartDeer Team',
      category: '出海指南',
      tags: ['新加坡', '企业出海', '东南亚', '金融中心'],
      seoDescription: '新加坡作为东南亚的金融中心和贸易枢纽，为中国企业出海提供了独特的优势和机遇。了解如何利用新加坡作为出海跳板。'
    }
  };
  
  return articles[slug] || null;
};

export async function generateMetadata({ params }: ArticlePageProps): Promise<Metadata> {
  const article = await getArticleBySlug(params.slug);
  
  if (!article) {
    return {
      title: 'Article Not Found',
    };
  }

  return {
    title: article.title,
    description: article.seoDescription,
    openGraph: {
      title: article.title,
      description: article.seoDescription,
      images: article.image ? [article.image] : [],
      type: 'article',
      publishedTime: article.publishDate,
      authors: [article.author],
    },
    twitter: {
      card: 'summary_large_image',
      title: article.title,
      description: article.seoDescription,
      images: article.image ? [article.image] : [],
    },
  };
}

export default async function ArticlePage({ params }: ArticlePageProps) {
  const article = await getArticleBySlug(params.slug);
  
  if (!article) {
    notFound();
  }

  const getContent = () => {
    const content = {
      zh: {
        readTime: '阅读时间',
        minutes: '分钟',
        publishedOn: '发布于',
        author: '作者',
        category: '分类',
        tags: '标签',
        relatedArticles: '相关文章',
        contactTitle: '需要专业咨询？',
        contactDescription: '我们的专家团队可以为您提供详细的出海指南和定制化解决方案。',
        contactButton: '联系专家'
      },
      en: {
        readTime: 'Read Time',
        minutes: 'minutes',
        publishedOn: 'Published on',
        author: 'Author',
        category: 'Category',
        tags: 'Tags',
        relatedArticles: 'Related Articles',
        contactTitle: 'Need Professional Consultation?',
        contactDescription: 'Our expert team can provide you with detailed overseas expansion guides and customized solutions.',
        contactButton: 'Contact Expert'
      },
      ja: {
        readTime: '読了時間',
        minutes: '分',
        publishedOn: '公開日',
        author: '著者',
        category: 'カテゴリー',
        tags: 'タグ',
        relatedArticles: '関連記事',
        contactTitle: '専門的なコンサルテーションが必要ですか？',
        contactDescription: '私たちの専門チームが、詳細な海外展開ガイドとカスタマイズされたソリューションを提供します。',
        contactButton: '専門家に相談'
      }
    };
    
    return content[params.locale] || content.zh;
  };

  const content = getContent();
  const estimatedReadTime = Math.ceil(article.content.length / 1000); // 简单估算阅读时间

  return (
    <div className="min-h-screen bg-white">
      <Header locale={params.locale} />
      
      <main className="pt-20">
        {/* Article Header */}
        <section className="py-16 lg:py-24 bg-gray-50">
          <div className="container-responsive">
            <div className="max-w-4xl mx-auto">
              {/* Article Meta */}
              <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6">
                <span>{content.publishedOn} {article.publishDate}</span>
                <span>•</span>
                <span>{content.readTime}: {estimatedReadTime} {content.minutes}</span>
                <span>•</span>
                <span>{content.category}: {article.category}</span>
              </div>
              
              {/* Article Title */}
              <h1 className="text-3xl lg:text-5xl font-bold text-gray-900 mb-8 leading-tight">
                {article.title}
              </h1>
              
              {/* Article Image */}
              {article.image && (
                <div className="relative h-64 lg:h-96 mb-8 rounded-lg overflow-hidden">
                  <Image
                    src={article.image}
                    alt={article.title}
                    fill
                    className="object-cover"
                  />
                </div>
              )}
            </div>
          </div>
        </section>

        {/* Article Content */}
        <section className="py-16">
          <div className="container-responsive">
            <div className="max-w-4xl mx-auto">
              <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
                {/* Main Content */}
                <div className="lg:col-span-3">
                  <div 
                    className="prose prose-lg max-w-none prose-headings:text-gray-900 prose-p:text-gray-700 prose-a:text-orange-500 hover:prose-a:text-orange-600"
                    dangerouslySetInnerHTML={{ __html: article.content }}
                  />
                  
                  {/* Tags */}
                  {article.tags.length > 0 && (
                    <div className="mt-12 pt-8 border-t border-gray-200">
                      <h3 className="text-lg font-bold text-gray-900 mb-4">{content.tags}</h3>
                      <div className="flex flex-wrap gap-2">
                        {article.tags.map((tag, index) => (
                          <span
                            key={index}
                            className="px-3 py-1 bg-orange-100 text-orange-700 rounded-full text-sm font-medium"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Sidebar */}
                <div className="lg:col-span-1">
                  <div className="sticky top-24 space-y-8">
                    {/* Contact Form */}
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h3 className="text-lg font-bold text-gray-900 mb-4">
                        {content.contactTitle}
                      </h3>
                      <p className="text-gray-600 mb-6 text-sm">
                        {content.contactDescription}
                      </p>
                      <ContactForm locale={params.locale} />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer locale={params.locale} />
    </div>
  );
}
