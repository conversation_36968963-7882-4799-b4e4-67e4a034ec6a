import React from 'react';
import { Metadata } from 'next';
import Link from 'next/link';
import { Locale } from '@/types';
import { generateMetadata as generateSeoMetadata } from '@/utils/seo';
import { formatDate } from '@/utils';

interface ArticlesPageProps {
  params: Promise<{ locale: Locale }>;
}

export async function generateMetadata({ params }: ArticlesPageProps): Promise<Metadata> {
  const { locale } = await params;
  
  const titles = {
    zh: '文章资讯 - SmartDeer 全球人力资源服务',
    en: 'Articles & Insights - SmartDeer Global HR Services',
    ja: 'お役立ち資料 - SmartDeer グローバル人事サービス'
  };
  
  const descriptions = {
    zh: '获取最新的全球人力资源行业资讯、政策解读、最佳实践案例和专业见解。',
    en: 'Get the latest global HR industry news, policy interpretations, best practice cases and professional insights.',
    ja: '最新のグローバル人事業界ニュース、政策解釈、ベストプラクティス事例、専門的な洞察を取得します。'
  };

  return generateSeoMetadata({
    title: titles[locale] || titles.zh,
    description: descriptions[locale] || descriptions.zh
  }, locale);
}

export default async function ArticlesPage({ params }: ArticlesPageProps) {
  const { locale } = await params;

  const getContent = () => {
    const content = {
      zh: {
        title: '文章资讯',
        subtitle: '全球人力资源行业洞察',
        description: '获取最新的行业资讯、政策解读和专业见解，助力您的全球化业务决策。',
        categories: [
          { name: '全部', value: 'all' },
          { name: '政策解读', value: 'policy' },
          { name: '行业趋势', value: 'trends' },
          { name: '案例分析', value: 'cases' },
          { name: '最佳实践', value: 'practices' }
        ],
        articles: [
          {
            id: 'cG9zdDoxNzM1',
            title: '中国企业为什么纷纷选择出海？',
            excerpt: '探讨中国企业出海的深层原因和发展趋势，分析全球化背景下的机遇与挑战。',
            image: 'https://blog.smartdeer.work/wp-content/uploads/2025/01/中国企业为什么纷纷选择出海？.png',
            category: '行业趋势',
            date: '2025-01-15',
            readTime: 5,
            slug: 'Why_do_Chinese_companies_choose_to_go_overseas'
          },
          {
            id: 'cG9zdDoxNzMz',
            title: '中国企业出海东南亚可以参考oppo的布局',
            excerpt: 'OPPO在东南亚市场的成功布局为其他中国企业提供了宝贵的经验和参考。',
            image: 'https://blog.smartdeer.work/wp-content/uploads/2025/01/中国企业出海东南亚可以参考-Oppo-的布局.png',
            category: '案例分析',
            date: '2025-01-12',
            readTime: 6,
            slug: 'Chinese_companies_going_overseas_to_Southeast_Asia_can_refer_to_OPPO_layout'
          },
          {
            id: 'cG9zdDoxNzMx',
            title: '中东搞钱到底去哪个城市？',
            excerpt: '分析中东地区各主要城市的商业机会和投资环境，为企业选择提供参考。',
            image: 'https://blog.smartdeer.work/wp-content/uploads/2025/01/中东搞钱到底去哪个城市？.png',
            category: '市场分析',
            date: '2025-01-10',
            readTime: 4,
            slug: 'Which_city_in_the_Middle_East_should_we_go_to_to_make_money'
          },
          {
            id: 'cG9zdDoxNzI5',
            title: '斋月期间和中东小伙伴打交道，get这4个重点',
            excerpt: '了解斋月期间的文化习俗和商务礼仪，助力企业在中东市场的成功合作。',
            image: 'https://blog.smartdeer.work/wp-content/uploads/2025/01/斋月期间和中东小伙伴打交道，Get-这4个重点.png',
            category: '文化指南',
            date: '2025-01-08',
            readTime: 3,
            slug: 'When_dealing_with_Middle_Eastern_friends_during_Ramadan'
          },
          {
            id: 'cG9zdDoxNzI1',
            title: '新加坡，中国企业出海的重要跳板',
            excerpt: '新加坡作为东南亚金融中心，为中国企业出海提供了独特的优势和机遇。',
            image: 'https://blog.smartdeer.work/wp-content/uploads/2025/01/新加坡，中国企业出海的重要跳板.png',
            category: '出海指南',
            date: '2025-01-05',
            readTime: 5,
            slug: 'Singapore_an_important_springboard_for_Chinese_companies_to_go_global'
          },
          {
            id: 'cG9zdDoxNjQ4',
            title: '沙特为什么是中国出海的很大的机会呢',
            excerpt: '深度分析沙特阿拉伯市场的投资机会和发展前景，为企业出海提供战略指导。',
            image: 'https://blog.smartdeer.work/wp-content/uploads/2024/08/WechatIMG1014.png',
            category: '投资机会',
            date: '2024-08-15',
            readTime: 6,
            slug: 'Why-Saudi-Arabia-is-a-great-opportunity-for-China-to-go-overseas'
          },
          {
            id: '1',
            title: '2024年全球远程工作政策变化趋势分析',
            excerpt: '深入分析全球主要国家在2024年对远程工作政策的调整，以及对企业全球化用工的影响...',
            category: '政策解读',
            publishedAt: '2024-01-15',
            readTime: '8分钟阅读',
            image: '/images/articles/remote-work-2024.jpg'
          },
          {
            id: '2',
            title: '新加坡EOR服务完整指南：从入门到精通',
            excerpt: '全面解析新加坡EOR（名义雇主）服务的各个方面，包括法律要求、成本分析、操作流程等...',
            category: '最佳实践',
            publishedAt: '2024-01-10',
            readTime: '12分钟阅读',
            image: '/images/articles/singapore-eor-guide.jpg'
          },
          {
            id: '3',
            title: '欧盟GDPR对全球人力资源管理的影响',
            excerpt: '详细分析欧盟通用数据保护条例（GDPR）对跨国企业人力资源管理带来的挑战和应对策略...',
            category: '政策解读',
            publishedAt: '2024-01-05',
            readTime: '10分钟阅读',
            image: '/images/articles/gdpr-hr-impact.jpg'
          },
          {
            id: '4',
            title: '科技公司全球扩张的人才战略案例研究',
            excerpt: '通过分析多家知名科技公司的全球扩张经验，总结出成功的人才获取和管理策略...',
            category: '案例分析',
            publishedAt: '2023-12-28',
            readTime: '15分钟阅读',
            image: '/images/articles/tech-global-expansion.jpg'
          }
        ],
        readMore: '阅读更多',
        noArticles: '暂无文章',
        loadMore: '加载更多'
      },
      en: {
        title: 'Articles & Insights',
        subtitle: 'Global HR Industry Insights',
        description: 'Get the latest industry news, policy interpretations and professional insights to support your global business decisions.',
        categories: [
          { name: 'All', value: 'all' },
          { name: 'Policy Analysis', value: 'policy' },
          { name: 'Industry Trends', value: 'trends' },
          { name: 'Case Studies', value: 'cases' },
          { name: 'Best Practices', value: 'practices' }
        ],
        articles: [
          {
            id: '1',
            title: '2024 Global Remote Work Policy Changes Trend Analysis',
            excerpt: 'In-depth analysis of remote work policy adjustments by major countries in 2024 and their impact on global employment...',
            category: 'Policy Analysis',
            publishedAt: '2024-01-15',
            readTime: '8 min read',
            image: '/images/articles/remote-work-2024.jpg'
          },
          {
            id: '2',
            title: 'Complete Guide to Singapore EOR Services: From Beginner to Expert',
            excerpt: 'Comprehensive analysis of all aspects of Singapore EOR (Employer of Record) services, including legal requirements, cost analysis, operational processes...',
            category: 'Best Practices',
            publishedAt: '2024-01-10',
            readTime: '12 min read',
            image: '/images/articles/singapore-eor-guide.jpg'
          },
          {
            id: '3',
            title: 'Impact of EU GDPR on Global Human Resource Management',
            excerpt: 'Detailed analysis of the challenges and response strategies that the EU General Data Protection Regulation (GDPR) brings to multinational HR management...',
            category: 'Policy Analysis',
            publishedAt: '2024-01-05',
            readTime: '10 min read',
            image: '/images/articles/gdpr-hr-impact.jpg'
          },
          {
            id: '4',
            title: 'Case Study of Talent Strategies for Tech Companies\' Global Expansion',
            excerpt: 'By analyzing the global expansion experiences of several well-known tech companies, we summarize successful talent acquisition and management strategies...',
            category: 'Case Studies',
            publishedAt: '2023-12-28',
            readTime: '15 min read',
            image: '/images/articles/tech-global-expansion.jpg'
          }
        ],
        readMore: 'Read More',
        noArticles: 'No articles available',
        loadMore: 'Load More'
      },
      ja: {
        title: 'お役立ち資料',
        subtitle: 'グローバル人事業界の洞察',
        description: '最新の業界ニュース、政策解釈、専門的な洞察を取得し、グローバルビジネスの意思決定をサポートします。',
        categories: [
          { name: 'すべて', value: 'all' },
          { name: '政策解釈', value: 'policy' },
          { name: '業界トレンド', value: 'trends' },
          { name: 'ケーススタディ', value: 'cases' },
          { name: 'ベストプラクティス', value: 'practices' }
        ],
        articles: [
          {
            id: '1',
            title: '2024年グローバルリモートワーク政策変化トレンド分析',
            excerpt: '2024年における主要国のリモートワーク政策調整とグローバル雇用への影響を詳細分析...',
            category: '政策解釈',
            publishedAt: '2024-01-15',
            readTime: '8分で読む',
            image: '/images/articles/remote-work-2024.jpg'
          },
          {
            id: '2',
            title: 'シンガポールEORサービス完全ガイド：初心者から専門家まで',
            excerpt: 'シンガポールEOR（名義雇用主）サービスのあらゆる側面を包括的に分析、法的要件、コスト分析、運用プロセスなど...',
            category: 'ベストプラクティス',
            publishedAt: '2024-01-10',
            readTime: '12分で読む',
            image: '/images/articles/singapore-eor-guide.jpg'
          },
          {
            id: '3',
            title: 'EU GDPRがグローバル人事管理に与える影響',
            excerpt: 'EU一般データ保護規則（GDPR）が多国籍企業の人事管理にもたらす課題と対応戦略を詳細分析...',
            category: '政策解釈',
            publishedAt: '2024-01-05',
            readTime: '10分で読む',
            image: '/images/articles/gdpr-hr-impact.jpg'
          },
          {
            id: '4',
            title: 'テック企業のグローバル展開における人材戦略ケーススタディ',
            excerpt: '複数の有名テック企業のグローバル展開経験を分析し、成功する人材獲得・管理戦略をまとめる...',
            category: 'ケーススタディ',
            publishedAt: '2023-12-28',
            readTime: '15分で読む',
            image: '/images/articles/tech-global-expansion.jpg'
          }
        ],
        readMore: 'もっと読む',
        noArticles: '記事がありません',
        loadMore: 'もっと読み込む'
      }
    };
    
    return content[locale] || content.zh;
  };

  const content = getContent();

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-orange-50 py-16 lg:py-24">
        <div className="container-responsive text-center">
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            {content.title}
          </h1>
          <p className="text-xl text-gray-600 mb-6">
            {content.subtitle}
          </p>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto">
            {content.description}
          </p>
        </div>
      </section>

      {/* Categories Filter */}
      <section className="py-8 bg-white border-b border-gray-200">
        <div className="container-responsive">
          <div className="flex flex-wrap justify-center gap-4">
            {content.categories.map((category, index) => (
              <button
                key={index}
                className={`px-6 py-2 rounded-full font-medium transition-colors ${
                  index === 0
                    ? 'bg-orange-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Articles Grid */}
      <section className="py-16 lg:py-24">
        <div className="container-responsive">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {content.articles.map((article) => (
              <article key={article.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                <div className="h-48 bg-gradient-to-br from-blue-100 to-orange-100 flex items-center justify-center">
                  <span className="text-gray-500 text-sm">Article Image</span>
                </div>
                
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="px-3 py-1 bg-orange-100 text-orange-800 text-xs font-medium rounded-full">
                      {article.category}
                    </span>
                    <span className="text-gray-500 text-sm">
                      {article.readTime}
                    </span>
                  </div>
                  
                  <h2 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                    {article.title}
                  </h2>
                  
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {article.excerpt}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-gray-500 text-sm">
                      {article.publishedAt ? formatDate(article.publishedAt, locale === 'zh' ? 'zh-CN' : locale === 'ja' ? 'ja-JP' : 'en-US') : ''}
                    </span>
                    <Link
                      href={`/${locale}/articles/${article.id}`}
                      className="text-orange-500 font-medium hover:text-orange-600 transition-colors"
                    >
                      {content.readMore} →
                    </Link>
                  </div>
                </div>
              </article>
            ))}
          </div>
          
          {/* Load More Button */}
          <div className="text-center mt-12">
            <button className="bg-orange-500 text-white px-8 py-3 rounded-lg font-bold hover:bg-orange-600 transition-colors">
              {content.loadMore}
            </button>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-16 lg:py-24 bg-gray-50">
        <div className="container-responsive text-center">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
            {locale === 'zh' ? '订阅我们的资讯' : locale === 'en' ? 'Subscribe to Our Newsletter' : 'ニュースレターを購読'}
          </h2>
          <p className="text-lg text-gray-700 mb-8 max-w-2xl mx-auto">
            {locale === 'zh' 
              ? '获取最新的全球人力资源行业资讯和专业见解，直接发送到您的邮箱。'
              : locale === 'en'
              ? 'Get the latest global HR industry news and professional insights delivered directly to your inbox.'
              : '最新のグローバル人事業界ニュースと専門的な洞察を直接メールボックスにお届けします。'
            }
          </p>
          <div className="max-w-md mx-auto flex gap-4">
            <input
              type="email"
              placeholder={locale === 'zh' ? '输入您的邮箱' : locale === 'en' ? 'Enter your email' : 'メールアドレスを入力'}
              className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            />
            <button className="bg-orange-500 text-white px-6 py-3 rounded-lg font-bold hover:bg-orange-600 transition-colors">
              {locale === 'zh' ? '订阅' : locale === 'en' ? 'Subscribe' : '購読'}
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}