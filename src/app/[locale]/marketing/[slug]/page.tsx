import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Image from 'next/image';
import { Locale } from '@/types';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ContactForm from '@/components/ContactForm';

interface MarketingPageProps {
  params: {
    locale: Locale;
    slug: string;
  };
}

interface MarketingContent {
  id: string;
  title: string;
  content: string;
  bannerImage?: string;
  publishDate: string;
  category: string;
  tags: string[];
  seoDescription: string;
}

// 模拟营销内容数据 - 实际应该从数据库或API获取
const getMarketingContentBySlug = async (slug: string): Promise<MarketingContent | null> => {
  const contents: Record<string, MarketingContent> = {
    'SmartDeer-Joins-CASFT': {
      id: 'cG9zdDoxNzcx',
      title: 'SmartDeer Joined China Association of Foreign Service Trades',
      content: `
        <p>With the accelerated pace of internationalization of Chinese enterprises, expanding HR-related services to overseas markets has become an important way for HR service providers to expand their services and find new business growth points. Recently, the Ninth General Meeting of China Association of Foreign Service Trades (short name CAFST) and the International Business Exchange Meeting of HR Service Organizations were held in Beijing, and <PERSON>D<PERSON>, as an industry leader in global HR services, was invited to attend the meeting and formally joined the association.</p>

        <figure class="wp-block-image size-full">
          <img loading="lazy" decoding="async" width="1581" height="889" src="https://blog.smartdeer.work/wp-content/uploads/2024/12/铜牌-1.jpg" alt="SmartDeer CAFST Certificate" />
        </figure>

        <h2>About China Association of Foreign Service Trades (CAFST)</h2>
        <p>Founded in 1989, China Association of Foreign Service Trades (short name CAFST) is a non-government organization which was registered at the Ministry of Civil Affairs of China. By now, as a national association representing human resources service industry in China, CAFST has 160 members across the country. In 1999, approved by the Ministry of Foreign Affairs, CAFST joined Ciett (the former body of World Employment Confederation) as a member of national federations. By such platform, CAFST plays an important role on facilitating exchange and cooperation between Chinese human resources service companies and global peers.</p>

        <div class="wp-block-gallery has-nested-images columns-default is-cropped">
          <figure class="wp-block-image size-large">
            <img loading="lazy" decoding="async" width="4000" height="2666" src="https://blog.smartdeer.work/wp-content/uploads/2024/12/卢美延.jpg" alt="CAFST Meeting" />
          </figure>
          <figure class="wp-block-image size-large">
            <img loading="lazy" decoding="async" width="4000" height="2667" src="https://blog.smartdeer.work/wp-content/uploads/2024/12/外服2.jpg" alt="CAFST Conference" />
          </figure>
        </div>

        <h2>About SmartDeer</h2>
        <p>SmartDeer, founded in 2021, is a one-stop "HR service and SaaS" platform dedicated to global recruitment and employment solutions. Incubated by Trustbridge Partners, with investments led by Welight Capital, WeWork, and Hash Global. SmartDeer is dual-headquartered in Hong Kong and Singapore, providing us with a strategic global advantage while ensuring compliance with local data storage and processing regulations.</p>

        <p>In addition to subsidiaries and branches in regions like the United States, the United Kingdom, the UAE, Saudi Arabia, Australia, Japan, South Korea, Thailand, Malaysia, Indonesia, the Philippines, Vietnam, Mainland China, Mexico and Brazil. Our services, delivered through our self-operated network and partners, span over 150 countries and regions worldwide.</p>

        <h2>Strategic Significance</h2>
        <p>Joining CAFST represents a significant milestone for SmartDeer as we continue to expand our global footprint and strengthen our position in the international HR services market. This membership will enable us to:</p>
        <ul>
          <li>Enhance collaboration with leading Chinese HR service providers</li>
          <li>Access valuable industry insights and best practices</li>
          <li>Participate in policy discussions affecting the HR services sector</li>
          <li>Strengthen our capabilities in serving Chinese enterprises going global</li>
        </ul>

        <h2>Looking Forward</h2>
        <p>As a member of CAFST, SmartDeer is committed to contributing to the development of China's foreign service trade industry while continuing to provide world-class HR solutions to our global clients. We look forward to collaborating with fellow association members to drive innovation and excellence in the HR services sector.</p>
      `,
      bannerImage: 'https://blog.smartdeer.work/wp-content/uploads/2024/12/铜牌2.jpg',
      publishDate: '2024-12-15',
      category: 'Industry Events',
      tags: ['CAFST', 'Partnership', 'Industry Association', 'Global HR'],
      seoDescription: 'SmartDeer joins China Association of Foreign Service Trades (CAFST) as an industry leader in global HR services, strengthening our position in the international market.'
    },
    'SmartDeer-with-E-town-Link-The-World': {
      id: 'cG9zdDoxNzU1',
      title: 'SmartDeer attended the "E-town Link The World"',
      content: `
        <p>SmartDeer was honored to participate in the "E-town Link The World" event, a prestigious gathering that brings together industry leaders, innovators, and global partners to explore opportunities in the digital economy and international business expansion.</p>

        <h2>Event Overview</h2>
        <p>The "E-town Link The World" event serves as a crucial platform for fostering international cooperation and promoting the development of the digital economy. This year's event focused on emerging technologies, global market trends, and strategies for successful international expansion.</p>

        <h2>SmartDeer's Participation</h2>
        <p>As a leading global HR services provider, SmartDeer participated in panel discussions and networking sessions, sharing insights on:</p>
        <ul>
          <li>Global talent acquisition strategies</li>
          <li>Cross-border employment compliance</li>
          <li>HR technology innovations</li>
          <li>Supporting enterprises in their global expansion journey</li>
        </ul>

        <h2>Key Takeaways</h2>
        <p>The event provided valuable opportunities to connect with industry peers, explore potential partnerships, and gain insights into emerging market trends. SmartDeer's participation reinforces our commitment to staying at the forefront of global HR services innovation.</p>

        <h2>Building Global Connections</h2>
        <p>Through events like "E-town Link The World," SmartDeer continues to build meaningful relationships with partners worldwide, enabling us to better serve our clients' global expansion needs and contribute to the development of the international business ecosystem.</p>
      `,
      bannerImage: 'https://blog.smartdeer.work/wp-content/uploads/2024/12/蓝鲸原1.jpg',
      publishDate: '2024-12-10',
      category: 'Industry Events',
      tags: ['E-town', 'Global Expansion', 'Digital Economy', 'Networking'],
      seoDescription: 'SmartDeer participated in the "E-town Link The World" event, exploring global opportunities and sharing insights on international HR services.'
    },
    'Fintech-Globalization-with-SmartDeer-and-Alibaba-Cloud': {
      id: 'cG9zdDoxNTQz',
      title: 'SmartDeer Invited to Join Alibaba Cloud Go Global Alliance',
      content: `
        <p>SmartDeer is proud to announce our invitation to join the prestigious Alibaba Cloud Go Global Alliance, marking a significant milestone in our mission to support fintech companies in their global expansion journey.</p>

        <h2>About the Alibaba Cloud Go Global Alliance</h2>
        <p>The Alibaba Cloud Go Global Alliance is a strategic initiative designed to help Chinese enterprises expand internationally by providing comprehensive cloud services, technical support, and business resources. The alliance brings together leading service providers across various industries to create a robust ecosystem for global expansion.</p>

        <h2>SmartDeer's Role in the Alliance</h2>
        <p>As a member of this elite alliance, SmartDeer will provide specialized HR services to fintech companies looking to expand globally. Our services include:</p>
        <ul>
          <li>Global talent acquisition and recruitment</li>
          <li>Employer of Record (EOR) services</li>
          <li>International payroll and benefits management</li>
          <li>Compliance and regulatory guidance</li>
          <li>HR technology solutions</li>
        </ul>

        <h2>Supporting Fintech Globalization</h2>
        <p>The fintech industry presents unique challenges when expanding internationally, including complex regulatory requirements, diverse talent needs, and varying market conditions. SmartDeer's expertise in global HR services positions us perfectly to address these challenges and support fintech companies in their international growth.</p>

        <h2>Strategic Partnership Benefits</h2>
        <p>This partnership with Alibaba Cloud enables SmartDeer to:</p>
        <ul>
          <li>Access a broader network of fintech companies seeking global expansion</li>
          <li>Leverage Alibaba Cloud's technical infrastructure and resources</li>
          <li>Collaborate with other alliance members to provide comprehensive solutions</li>
          <li>Contribute to the development of the global fintech ecosystem</li>
        </ul>

        <h2>Looking Ahead</h2>
        <p>We are excited about the opportunities this partnership presents and look forward to working closely with Alibaba Cloud and fellow alliance members to support the global ambitions of fintech companies worldwide.</p>
      `,
      bannerImage: 'https://blog.smartdeer.work/wp-content/uploads/2024/08/阿里云3.png',
      publishDate: '2024-08-15',
      category: 'Partnerships',
      tags: ['Alibaba Cloud', 'Fintech', 'Global Alliance', 'Partnership'],
      seoDescription: 'SmartDeer joins Alibaba Cloud Go Global Alliance to provide specialized HR services for fintech companies expanding internationally.'
    }
  };
  
  return contents[slug] || null;
};

export async function generateMetadata({ params }: MarketingPageProps): Promise<Metadata> {
  const content = await getMarketingContentBySlug(params.slug);
  
  if (!content) {
    return {
      title: 'Marketing Content Not Found',
    };
  }

  return {
    title: content.title,
    description: content.seoDescription,
    openGraph: {
      title: content.title,
      description: content.seoDescription,
      images: content.bannerImage ? [content.bannerImage] : [],
      type: 'article',
      publishedTime: content.publishDate,
    },
    twitter: {
      card: 'summary_large_image',
      title: content.title,
      description: content.seoDescription,
      images: content.bannerImage ? [content.bannerImage] : [],
    },
  };
}

export default async function MarketingDetailPage({ params }: MarketingPageProps) {
  const content = await getMarketingContentBySlug(params.slug);
  
  if (!content) {
    notFound();
  }

  const getPageContent = () => {
    const pageContent = {
      zh: {
        publishedOn: '发布于',
        category: '分类',
        tags: '标签',
        contactTitle: '需要专业咨询？',
        contactDescription: '我们的专家团队可以为您提供详细的合作方案和定制化解决方案。',
        contactButton: '联系专家'
      },
      en: {
        publishedOn: 'Published on',
        category: 'Category',
        tags: 'Tags',
        contactTitle: 'Need Professional Consultation?',
        contactDescription: 'Our expert team can provide you with detailed partnership proposals and customized solutions.',
        contactButton: 'Contact Expert'
      },
      ja: {
        publishedOn: '公開日',
        category: 'カテゴリー',
        tags: 'タグ',
        contactTitle: '専門的なコンサルテーションが必要ですか？',
        contactDescription: '私たちの専門チームが、詳細なパートナーシップ提案とカスタマイズされたソリューションを提供します。',
        contactButton: '専門家に相談'
      }
    };
    
    return pageContent[params.locale] || pageContent.zh;
  };

  const pageContent = getPageContent();

  return (
    <div className="min-h-screen bg-white">
      <Header locale={params.locale} />
      
      <main className="pt-20">
        {/* Banner */}
        {content.bannerImage && (
          <section className="relative h-64 lg:h-80">
            <Image
              src={content.bannerImage}
              alt={content.title}
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-black bg-opacity-40" />
          </section>
        )}

        {/* Content Header */}
        <section className="py-16 lg:py-24 bg-gray-50">
          <div className="container-responsive">
            <div className="max-w-4xl mx-auto">
              {/* Meta Info */}
              <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6">
                <span>{pageContent.publishedOn} {content.publishDate}</span>
                <span>•</span>
                <span className="px-2 py-1 bg-orange-100 text-orange-700 rounded-full text-xs font-medium">
                  {content.category}
                </span>
              </div>
              
              {/* Title */}
              <h1 className="text-3xl lg:text-5xl font-bold text-gray-900 mb-8 leading-tight">
                {content.title}
              </h1>
            </div>
          </div>
        </section>

        {/* Content */}
        <section className="py-16">
          <div className="container-responsive">
            <div className="max-w-4xl mx-auto">
              <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
                {/* Main Content */}
                <div className="lg:col-span-3">
                  <div 
                    className="prose prose-lg max-w-none prose-headings:text-gray-900 prose-p:text-gray-700 prose-a:text-orange-500 hover:prose-a:text-orange-600 prose-img:rounded-lg prose-img:shadow-lg"
                    dangerouslySetInnerHTML={{ __html: content.content }}
                  />
                  
                  {/* Tags */}
                  {content.tags.length > 0 && (
                    <div className="mt-12 pt-8 border-t border-gray-200">
                      <h3 className="text-lg font-bold text-gray-900 mb-4">{pageContent.tags}</h3>
                      <div className="flex flex-wrap gap-2">
                        {content.tags.map((tag, index) => (
                          <span
                            key={index}
                            className="px-3 py-1 bg-orange-100 text-orange-700 rounded-full text-sm font-medium"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Sidebar */}
                <div className="lg:col-span-1">
                  <div className="sticky top-24">
                    {/* Contact Form */}
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h3 className="text-lg font-bold text-gray-900 mb-4">
                        {pageContent.contactTitle}
                      </h3>
                      <p className="text-gray-600 mb-6 text-sm">
                        {pageContent.contactDescription}
                      </p>
                      <ContactForm locale={params.locale} />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer locale={params.locale} />
    </div>
  );
}
