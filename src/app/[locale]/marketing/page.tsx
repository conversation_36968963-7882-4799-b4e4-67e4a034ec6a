import React from 'react';
import { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';
import { Locale } from '@/types';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

interface MarketingPageProps {
  params: {
    locale: Locale;
  };
}

interface MarketingItem {
  id: string;
  title: string;
  excerpt: string;
  image: string;
  date: string;
  category: string;
  slug: string;
}

export async function generateMetadata({ params }: MarketingPageProps): Promise<Metadata> {
  const titles = {
    zh: '合作伙伴与活动 - SmartDeer 全球人力资源服务',
    en: 'Partnerships & Events - SmartDeer Global HR Services',
    ja: 'パートナーシップ・イベント - SmartDeer グローバル人事サービス'
  };
  
  const descriptions = {
    zh: '了解SmartDeer的合作伙伴关系、行业活动和企业动态，见证我们在全球人力资源服务领域的发展历程。',
    en: 'Learn about SmartDeer\'s partnerships, industry events and corporate updates. Witness our journey in the global HR services sector.',
    ja: 'SmartDeerのパートナーシップ、業界イベント、企業の最新情報をご覧ください。グローバル人事サービス分野での歩みをご覧ください。'
  };

  return {
    title: titles[params.locale] || titles.zh,
    description: descriptions[params.locale] || descriptions.zh,
    openGraph: {
      title: titles[params.locale] || titles.zh,
      description: descriptions[params.locale] || descriptions.zh,
      type: 'website',
    },
  };
}

export default function MarketingPage({ params }: MarketingPageProps) {
  const getContent = () => {
    const content = {
      zh: {
        title: '合作伙伴与活动',
        subtitle: '携手共创全球人力资源服务新未来',
        description: '了解SmartDeer的合作伙伴关系、行业活动和企业动态，见证我们在全球人力资源服务领域的发展历程。',
        categories: [
          { name: '全部', value: 'all' },
          { name: '合作伙伴', value: 'partnership' },
          { name: '行业活动', value: 'events' },
          { name: '企业动态', value: 'news' }
        ],
        marketingItems: [
          {
            id: 'cG9zdDoxNzcx',
            title: 'SmartDeer加入中国对外服务贸易协会',
            excerpt: '随着中国企业国际化步伐的加快，SmartDeer作为全球人力资源服务行业的领军企业，受邀参加会议并正式加入协会。',
            image: 'https://blog.smartdeer.work/wp-content/uploads/2024/12/铜牌2-1.jpg',
            date: '2024-12-15',
            category: '行业活动',
            slug: 'SmartDeer-Joins-CASFT'
          },
          {
            id: 'cG9zdDoxNzU1',
            title: 'SmartDeer参加"E-town链接世界"活动',
            excerpt: 'SmartDeer受邀参加"E-town链接世界"活动，与行业伙伴共同探讨全球化发展机遇。',
            image: 'https://blog.smartdeer.work/wp-content/uploads/2024/12/蓝鲸原1.jpg',
            date: '2024-12-10',
            category: '行业活动',
            slug: 'SmartDeer-with-E-town-Link-The-World'
          },
          {
            id: 'cG9zdDoxNTgy',
            title: 'SmartDeer与通商律师事务所合作，解读企业出海组织规划与合规',
            excerpt: '受邀参加通商律师事务所闭门沙龙，SmartDeer为中国企业"走出去"提供组织规划和合规解决方案。',
            image: 'https://blog.smartdeer.work/wp-content/uploads/2024/08/杭州1-1.png',
            date: '2024-08-20',
            category: '合作伙伴',
            slug: 'Compliance-with-SmartDeer-and-Tongshang'
          },
          {
            id: 'cG9zdDoxNTg1',
            title: '通商律师事务所、SmartDeer与宁波银行携手探索中国企业出海解决方案',
            excerpt: '三方合作，为中国企业出海提供法律、人力资源和金融服务的一站式解决方案。',
            image: 'https://blog.smartdeer.work/wp-content/uploads/2024/08/通商1-3.png',
            date: '2024-08-18',
            category: '合作伙伴',
            slug: 'Oversea-with-SmartDeer-Tongshang-and-NBCB'
          },
          {
            id: 'cG9zdDoxNTQz',
            title: 'SmartDeer受邀加入"阿里云出海联盟"',
            excerpt: 'SmartDeer受邀加入阿里云出海联盟，为金融科技企业全球化提供专业人力资源服务。',
            image: 'https://blog.smartdeer.work/wp-content/uploads/2024/08/阿里云3.png',
            date: '2024-08-15',
            category: '合作伙伴',
            slug: 'Fintech-Globalization-with-SmartDeer-and-Alibaba-Cloud'
          },
          {
            id: 'cG9zdDoxNTMx',
            title: 'SmartDeer与钉钉：为中国企业提供全球HR SaaS服务',
            excerpt: 'SmartDeer与钉钉深度合作，为中国企业提供全球化的人力资源SaaS解决方案。',
            image: 'https://blog.smartdeer.work/wp-content/uploads/2024/08/钉钉活动插图-1.jpg',
            date: '2024-08-12',
            category: '合作伙伴',
            slug: 'HR-SAAS-with-SmartDeer-and-DingDing'
          },
          {
            id: 'cG9zdDoxNTM3',
            title: 'SmartDeer与TGO联合举办"出海"闭门会议',
            excerpt: 'SmartDeer与TGO联合举办企业出海闭门会议，分享全球化经验和最佳实践。',
            image: 'https://blog.smartdeer.work/wp-content/uploads/2024/08/TGO1-1.png',
            date: '2024-08-10',
            category: '行业活动',
            slug: 'Enterprises-Going-Global-with-SmartDeer-and-TGO'
          },
          {
            id: 'cG9zdDoxNTI1',
            title: 'SmartDeer加入华北（天津）出海服务联盟',
            excerpt: 'SmartDeer作为首家也是唯一的人力资源服务商加入华北（天津）出海服务联盟。',
            image: 'https://blog.smartdeer.work/wp-content/uploads/2024/08/骞和1-2.jpg',
            date: '2024-08-08',
            category: '企业动态',
            slug: 'Global-Service-with-SmartDeer-and-SilkRoad360'
          }
        ],
        readMore: '了解更多',
        viewAll: '查看全部'
      },
      en: {
        title: 'Partnerships & Events',
        subtitle: 'Building the Future of Global HR Services Together',
        description: 'Learn about SmartDeer\'s partnerships, industry events and corporate updates. Witness our journey in the global HR services sector.',
        categories: [
          { name: 'All', value: 'all' },
          { name: 'Partnerships', value: 'partnership' },
          { name: 'Events', value: 'events' },
          { name: 'News', value: 'news' }
        ],
        marketingItems: [
          {
            id: 'cG9zdDoxNzcx',
            title: 'SmartDeer Joined China Association of Foreign Service Trades',
            excerpt: 'With the accelerated pace of internationalization of Chinese enterprises, SmartDeer was invited to attend the meeting and formally joined the association.',
            image: 'https://blog.smartdeer.work/wp-content/uploads/2024/12/铜牌2-1.jpg',
            date: '2024-12-15',
            category: 'Events',
            slug: 'SmartDeer-Joins-CASFT'
          },
          {
            id: 'cG9zdDoxNzU1',
            title: 'SmartDeer attended the "E-town Link The World"',
            excerpt: 'SmartDeer was invited to participate in the "E-town Link The World" event to explore global development opportunities with industry partners.',
            image: 'https://blog.smartdeer.work/wp-content/uploads/2024/12/蓝鲸原1.jpg',
            date: '2024-12-10',
            category: 'Events',
            slug: 'SmartDeer-with-E-town-Link-The-World'
          },
          {
            id: 'cG9zdDoxNTgy',
            title: 'SmartDeer Explains Organisational Planning and Compliance for Chinese Enterprises "Go Global"',
            excerpt: 'Invited to participate in Tongshang Law Firm\'s closed salon, SmartDeer provides organizational planning and compliance solutions.',
            image: 'https://blog.smartdeer.work/wp-content/uploads/2024/08/杭州1-1.png',
            date: '2024-08-20',
            category: 'Partnerships',
            slug: 'Compliance-with-SmartDeer-and-Tongshang'
          },
          {
            id: 'cG9zdDoxNTg1',
            title: 'Commerce & Finance Law Offices, SmartDeer and Bank of Ningbo Join Hands',
            excerpt: 'Three-party cooperation to provide one-stop solutions for Chinese enterprises going global.',
            image: 'https://blog.smartdeer.work/wp-content/uploads/2024/08/通商1-3.png',
            date: '2024-08-18',
            category: 'Partnerships',
            slug: 'Oversea-with-SmartDeer-Tongshang-and-NBCB'
          },
          {
            id: 'cG9zdDoxNTQz',
            title: 'SmartDeer Invited to Join Alibaba Cloud Go Global Alliance',
            excerpt: 'SmartDeer was invited to join the Alibaba Cloud Go Global Alliance to provide professional HR services for fintech globalization.',
            image: 'https://blog.smartdeer.work/wp-content/uploads/2024/08/阿里云3.png',
            date: '2024-08-15',
            category: 'Partnerships',
            slug: 'Fintech-Globalization-with-SmartDeer-and-Alibaba-Cloud'
          },
          {
            id: 'cG9zdDoxNTMx',
            title: 'SmartDeer & DingDing: Providing Global HR SAAS for Chinese Enterprises',
            excerpt: 'SmartDeer partners with DingDing to provide global HR SaaS solutions for Chinese enterprises.',
            image: 'https://blog.smartdeer.work/wp-content/uploads/2024/08/钉钉活动插图-1.jpg',
            date: '2024-08-12',
            category: 'Partnerships',
            slug: 'HR-SAAS-with-SmartDeer-and-DingDing'
          },
          {
            id: 'cG9zdDoxNTM3',
            title: '"Go Global" Closed Meeting held by SmartDeer and TGO',
            excerpt: 'SmartDeer and TGO jointly held a closed meeting on enterprise globalization, sharing experiences and best practices.',
            image: 'https://blog.smartdeer.work/wp-content/uploads/2024/08/TGO1-1.png',
            date: '2024-08-10',
            category: 'Events',
            slug: 'Enterprises-Going-Global-with-SmartDeer-and-TGO'
          },
          {
            id: 'cG9zdDoxNTI1',
            title: 'SmartDeer Joins North China (TEDA) Outbound Service Alliance',
            excerpt: 'SmartDeer joins as the first and only HR service provider in the North China (TEDA) Outbound Service Alliance.',
            image: 'https://blog.smartdeer.work/wp-content/uploads/2024/08/骞和1-2.jpg',
            date: '2024-08-08',
            category: 'News',
            slug: 'Global-Service-with-SmartDeer-and-SilkRoad360'
          }
        ],
        readMore: 'Read More',
        viewAll: 'View All'
      },
      ja: {
        title: 'パートナーシップ・イベント',
        subtitle: 'グローバル人事サービスの未来を共に築く',
        description: 'SmartDeerのパートナーシップ、業界イベント、企業の最新情報をご覧ください。グローバル人事サービス分野での歩みをご覧ください。',
        categories: [
          { name: 'すべて', value: 'all' },
          { name: 'パートナーシップ', value: 'partnership' },
          { name: 'イベント', value: 'events' },
          { name: 'ニュース', value: 'news' }
        ],
        marketingItems: [
          // Japanese translations would go here
        ],
        readMore: '詳細を見る',
        viewAll: 'すべて表示'
      }
    };
    
    return content[params.locale] || content.zh;
  };

  const content = getContent();

  return (
    <div className="min-h-screen bg-white">
      <Header locale={params.locale} />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-blue-50 to-orange-50 py-16 lg:py-24">
          <div className="container-responsive text-center">
            <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
              {content.title}
            </h1>
            <p className="text-xl text-gray-600 mb-6">
              {content.subtitle}
            </p>
            <p className="text-lg text-gray-700 max-w-3xl mx-auto">
              {content.description}
            </p>
          </div>
        </section>

        {/* Marketing Items Grid */}
        <section className="py-16 lg:py-24">
          <div className="container-responsive">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {content.marketingItems.map((item: MarketingItem) => (
                <article key={item.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                  <div className="relative h-48">
                    <Image
                      src={item.image}
                      alt={item.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="p-6">
                    <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                      <span>{item.date}</span>
                      <span>•</span>
                      <span className="px-2 py-1 bg-orange-100 text-orange-700 rounded-full text-xs font-medium">
                        {item.category}
                      </span>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                      {item.title}
                    </h3>
                    <p className="text-gray-600 mb-4 line-clamp-3">
                      {item.excerpt}
                    </p>
                    <Link
                      href={`/${params.locale}/marketing/${item.slug}`}
                      className="inline-flex items-center text-orange-500 hover:text-orange-600 font-medium"
                    >
                      {content.readMore}
                      <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </div>
                </article>
              ))}
            </div>
          </div>
        </section>
      </main>

      <Footer locale={params.locale} />
    </div>
  );
}
