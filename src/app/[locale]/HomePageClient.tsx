'use client';

import React, { useEffect, useState, useRef } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { Locale } from '@/types';
import CustomerList from '@/components/CustomerList';
import ContactForm from '@/components/ContactForm';

interface HomePageClientProps {
  locale: Locale;
}

// 移动端英文版本的数据结构
interface ServiceItem {
  id: string;
  title: string;
  description: string;
  image: string;
  alt: string;
  isVideo?: boolean;
}

interface AdvantageItem {
  icon: string;
  title: string;
  description: string;
  alt: string;
}

interface LifecycleItem {
  icon: string;
  title: string;
}

interface ProcessItem {
  num: string;
  title: string;
  description: string;
  image: string;
  alt: string;
}

interface SolutionCase {
  id: string;
  title: string;
  image: string;
  background: string;
  challenges: string[];
  keyProblems?: string[];
  solutions: Array<{
    title: string;
    items: string[];
  }>;
  results: string[];
  testimonial: string;
}

// 移动端滚动显示Hook
function useScrollShow(delayOffset = 0) {
  const ref = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setTimeout(() => setIsVisible(true), delayOffset);
        }
      },
      { threshold: 0.1 }
    );

    observer.observe(element);
    return () => observer.unobserve(element);
  }, [delayOffset]);

  return { ref, isVisible };
}

// 移动端英文版本主组件
export default function HomePageClient({ locale }: HomePageClientProps) {
  const [showContactForm, setShowContactForm] = useState(false);
  const [showConsultantCode, setShowConsultantCode] = useState(false);
  const [solutionExpandStatus, setSolutionExpandStatus] = useState({
    s1: false,
    s2: false,
    s3: false,
    s4: false,
    s5: false,
    s6: false
  });
  const [lifecycleItems, setLifecycleItems] = useState([0, 1, 2, 3, 4, 5, 6]);

  // 生命周期轮播效果
  useEffect(() => {
    const interval = setInterval(() => {
      setLifecycleItems(prev => prev.map(item => (item + 1) % 7));
    }, 2200);
    return () => clearInterval(interval);
  }, []);

  // 滚动监听
  useEffect(() => {
    const scrollItems: Array<{ offsetTop: number; el: HTMLElement }> = [];
    let timer: NodeJS.Timeout | null = null;

    const scroll = () => {
      if (timer) return;
      timer = setTimeout(() => {
        const offset = window.scrollY + window.innerHeight;
        scrollItems.forEach((item, index) => {
          if (item.offsetTop < offset) {
            item.el.setAttribute('show', 'true');
            scrollItems.splice(index, 1);
          }
        });
        timer = null;
      }, 30);
    };

    window.addEventListener('scroll', scroll);
    return () => {
      window.removeEventListener('scroll', scroll);
      if (timer) clearTimeout(timer);
    };
  }, []);

  // 聊天机器人
  const cozeWebSDK = useRef<any>(null);
  useEffect(() => {
    // 初始化聊天机器人
    if (typeof window !== 'undefined' && (window as any).CozeWebSDK) {
      cozeWebSDK.current = new (window as any).CozeWebSDK.WebChatClient({
        config: {
          botId: '7439335660751716386'
        },
        ui: {
          base: {
            icon: 'https://static.smartdeer.com/bot_logo.png',
            layout: 'mobile',
            zIndex: 1000
          },
          chatBot: {
            title: '顾问杰哥',
            uploadable: false,
          },
          asstBtn: {
            isNeed: false
          },
          footer: {
            isShow: true,
            expressionText: 'Powered by SmartDeer.'
          }
        }
      });
    }
  }, []);

  const toggleChat = () => {
    if (cozeWebSDK.current) {
      cozeWebSDK.current.showChatBot();
    }
  };

  const toggleSolution = (key: string) => {
    setSolutionExpandStatus(prev => ({
      ...prev,
      [key]: !prev[key as keyof typeof prev]
    }));
  };

  const getSolutionDescStyle = (key: string) => {
    const isExpanded = solutionExpandStatus[key as keyof typeof solutionExpandStatus];
    return {
      maxHeight: isExpanded ? '2000px' : '315px',
      overflow: 'hidden',
      transition: 'max-height 0.8s ease-out'
    };
  };

  const smoothScrollTo = (duration: number, target: number) => {
    const start = window.scrollY;
    const startTime = performance.now();

    function scrollStep(currentTime: number) {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const newScrollY = start + (target - start) * progress;
      window.scrollTo(0, newScrollY);
      if (progress < 1) {
        requestAnimationFrame(scrollStep);
      }
    }
    requestAnimationFrame(scrollStep);
  };

  const submitSuccess = () => {
    setShowContactForm(false);
    // 这里可以添加成功提示
  };

  // 移动端英文版本的数据
  const mobileEnglishContent = {
    hero: {
      slogan: 'Global Recruitment & Employment',
      title: 'International HR One-stop Service',
      description: 'Global recruitment hires the worldwide talents, we handle the compliance of global recruitment and salary and payroll, provide professional one-stop Human Resources solution.'
    },
    customer: {
      title: 'Serve Global Customers'
    },
    services: {
      title: 'Our Solutions',
      items: [
        {
          id: 'service-recruitment',
          title: 'Global Recruitment',
          description: 'Need to acquire talents globally? Our expert team ensures seamless hiring in your target markets. With an extensive talent pool and a skilled headhunting team, we manage recruitment and payments efficiently. Plus, enjoy 24/7 support for smooth operations.',
          image: '/images/index/recruitment.webp',
          alt: 'Global Recruitment'
        },
        {
          id: 'service-eor',
          title: 'Employer of Record',
          description: 'No legal setup abroad? We help you hire full-time employees legally while minimizing overhead costs. Our end-to-end services cover compliance, visas, onboarding, team management, payroll, taxes, and benefits. Automated tools streamline reporting and ensure smooth process management.',
          image: '/images/index/eor.webp',
          alt: 'EOR'
        },
        {
          id: 'service-contractor',
          title: 'Independent Contractor',
          description: 'Need short-term, flexible employment in a new market? We help you hire independent contractors quickly, even without a local legal team. We provide compliant contract templates and a streamlined online signing process to ensure global employees meet local regulations. Background checks are conducted to prevent legal risks, and we offer payroll services with transparent fees.',
          image: '/images/index/contractor.webp',
          alt: 'Contractor'
        },
        {
          id: 'service-peo',
          title: 'Human Resource Outsourcing',
          description: 'Already set up a legal entity but want to offload HR tasks? We handle your payroll, taxes, insurance, benefits, onboarding, and more. Our services are flexible and customized to meet your needs, ensuring compliance across regions while saving you time and costs.',
          image: '/images/index/peo.webp',
          alt: 'Human Resource Outsourcing'
        },
        {
          id: 'service-fintech',
          title: 'FinTech-Driven Global Payroll Solutions',
          description: 'SmartDeer\'s platform supports payroll processing in over 150 currencies, enabling seamless and efficient payments to employees and contractors worldwide. With competitive exchange rates, robust hedging capabilities, and batch payment functionality, SmartDeer significantly reduces international transfer costs and minimizes the impact of exchange rate fluctuations on payroll expenses. Additionally, SmartDeer\'s expertise in compliance and tax management ensures smooth global HR operations, making it a trusted partner for international employment solutions.',
          image: '/images/index/fintech.png',
          alt: 'FinTech-Driven Global Payroll Solutions'
        },
        {
          id: 'service-saas',
          title: 'Global HR SaaS',
          description: 'SmartDeer provides enterprises with professional, integrated solutions for global expansion through a unique "HR Services + SaaS System" model. The SmartDeer Global HR SaaS platform empowers businesses to achieve data-driven, digitalized global HR management. It enables global employees, HR teams, and line managers to efficiently manage HR processes and related tasks while maintaining and centralizing core HR data for streamlined operations.',
          image: 'https://static.smartdeer.com/Global_HR_SaaS.mp4',
          alt: 'Global HR SaaS',
          isVideo: true
        }
      ]
    },
    advantages: {
      title: 'Why choose us?',
      items: [
        {
          icon: '/images/index/icon-global.svg',
          title: 'Global Network',
          description: 'Supporting your business 24/7 across 150+ countries.',
          alt: 'Global Network'
        },
        {
          icon: '/images/index/icon-professional-team.svg',
          title: 'Compliance-Ready',
          description: 'Ensuring global policy and data compliance at every step.',
          alt: 'Compliance-Ready'
        },
        {
          icon: '/images/index/icon-service.svg',
          title: '24/7 Support',
          description: 'Bilingual service in Chinese and English for fast responses.',
          alt: '24/7 Support'
        },
        {
          icon: '/images/index/icon-price.svg',
          title: 'Competitive Pricing',
          description: 'High-quality services at the best possible rates.',
          alt: 'Competitive Pricing'
        }
      ]
    },
    lifecycle: {
      title: 'Full-Life Cycle Management',
      items: [
        { icon: '/images/index/recruitment.svg', title: 'Recruitment' },
        { icon: '/images/index/compliance.svg', title: 'Compliance' },
        { icon: '/images/index/contract.svg', title: 'Contracts Signing' },
        { icon: '/images/index/on-boarding.svg', title: 'On-boarding' },
        { icon: '/images/index/management.svg', title: 'Management' },
        { icon: '/images/index/payment.svg', title: 'Payment' },
        { icon: '/images/index/off-boarding.svg', title: 'Off-boarding' }
      ]
    },
    process: {
      title: 'How It Works',
      items: [
        {
          num: '01',
          title: 'Global Services Coverage, Localized Recruitment Across Industries',
          description: 'With 80+ recruitment consultants spanning China, Southeast Asia, North America, the Middle East, and beyond, we specialize in IT, SaaS, Games, Intelligent Manufacturing, and more. From operations and BD to sales and engineering roles, we offer tailored recruitment services worldwide.',
          image: '/images/index/en/recriument.png',
          alt: 'Global Services Coverage, Localized Recruitment Across Industries'
        },
        {
          num: '02',
          title: 'Visa Services & Compliance Support',
          description: 'We provide end-to-end visa assistance, background checks, and compliance handling. Our services streamline onboarding, ensuring smooth processes and legal security while minimizing risks for global hires.',
          image: '/images/index/compliance.webp',
          alt: 'Visa Services & Compliance Support'
        },
        {
          num: '03',
          title: 'Select & Customize Contracts, Sign in Just 3 Minutes',
          description: 'Choose a compliance-ready contract template based on your business model or employment plan. Complete the process—from drafting to electronic signing—within 3 minutes, fully supported by our online contracting system.',
          image: '/images/index/en/contract.png',
          alt: 'Select & Customize Contracts, Sign in Just 3 Minutes'
        },
        {
          num: '04',
          title: 'Streamline Onboarding with Automated Audits',
          description: 'Upload employee information effortlessly for automatic platform review. Our online onboarding system ensures timely approvals, so employees are set up and ready to work without delays.',
          image: '/images/index/en/progress.png',
          alt: 'Streamline Onboarding with Automated Audits'
        },
        {
          num: '05',
          title: 'Online Contract Management, Employee Records, Leave & Attendance',
          description: 'Easily track contract progress, manage employee records, and oversee personnel changes. Support multiple clocking methods, automate approval processes, and monitor attendance with real-time updates.',
          image: '/images/index/en/staff.png',
          alt: 'Online Contract Management, Employee Records, Leave & Attendance'
        },
        {
          num: '06',
          title: 'Compensation, Payroll & Insurance Processing',
          description: 'Streamline reimbursements, allowances, and expense approvals with self-service tools. Automate payroll management while staying compliant with regional laws and regulations. Enable multi-currency payments with transparent exchange fees.',
          image: '/images/index/en/pay.png',
          alt: 'Compensation, Payroll & Insurance Processing'
        },
        {
          num: '07',
          title: 'Off-Boarding Coordination & Final Payment Settlement',
          description: 'Easily manage resignations with scheduled applications, streamlined off-boarding, and final payments processed through the platform. Ensure compliance with local regulations at every step of the separation process.',
          image: '/images/index/en/dimission.png',
          alt: 'Off-Boarding Coordination & Final Payment Settlement'
        }
      ]
    },
    solution: {
      title: '行业案例',
      subtitle: 'Solution',
      cases: [
        {
          id: 's1',
          title: 'Client Success: A Leading Chinese ICT Solutions Provider – Best Practices in Global Team Expansion and Management',
          image: '/images/index/case1.png',
          background: 'Since launching its global expansion in 2020, this company has entered over 30 countries and regions, encountering specific needs such as:',
          challenges: [
            'Global Employment: EOR services and visa processing in 16 countries, along with HRO services in 5 countries.',
            'Employer Cost Assessment: Accurate multi-country employer cost calculations with expert advice on policy compliance.',
            'Policy Advisory & Rapid Support: Keeping up with changing employment policies worldwide and responding quickly.'
          ],
          keyProblems: [
            'Complex Administrative Tasks: Overseas HR teams were overwhelmed by daily inquiries requiring quick and accurate responses.',
            'Difficult Visa Applications: Target countries involved high-risk and complex visa processes.',
            'Heavy Reimbursement Workload: Large, concentrated reimbursement requests from business departments strained HR and finance teams.'
          ],
          solutions: [
            {
              title: 'EOR and HRO Services',
              items: [
                'Delivered EOR services and visa support across 16 countries.',
                'Provided localized HRO services in 5 countries, including payroll management, employee benefits, and labor contract handling.'
              ]
            },
            {
              title: 'Real-Time Policy Advisory',
              items: [
                'SmartDeer\'s expert team offered real-time advice on multi-country employment policies and tax regulations, enabling swift adjustments to hiring strategies.'
              ]
            },
            {
              title: 'Visa Support Services',
              items: [
                'Comprehensive visa application assistance, from document preparation to submission, focusing on high-complexity countries and improving approval rates.'
              ]
            },
            {
              title: 'Automated Data Management',
              items: [
                'SmartDeer\'s platform streamlined reimbursement workflows with automated data submission and analysis, reducing manual effort.'
              ]
            },
            {
              title: 'Efficient Support Mechanisms',
              items: [
                'Assigned a dedicated account manager and a 24/7 support team to ensure timely resolution of complex issues, with response times cut to under 24 hours.'
              ]
            }
          ],
          results: [
            'Efficient Global Operations: Successfully helped the company hire teams in 16 countries and complete visa processing, ensuring seamless market entry.',
            '40% Reduction in Time Costs: Automation significantly reduced HR teams\' administrative workload.',
            '95% Visa Approval Rate: Expert support boosted visa success rates in high-difficulty regions.',
            '50% Improvement in Reimbursement Efficiency: Automated data handling optimized reimbursement processes for business teams.'
          ],
          testimonial: '"SmartDeer has provided us with comprehensive support in our global expansion. From employment services to visa processing, they have demonstrated exceptional professionalism and efficiency. SmartDeer is an indispensable strategic partner in our international operations." —— Head of International Business'
        }
      ]
    }
  };

  return (
    <div className="index-page min-w-[375px] font-helvetica">
      {/* Header */}
      <header className="block relative bg-[#FFF3E6] w-full overflow-hidden">
        <div className="header-content relative z-10 w-full max-w-[680px] mx-auto px-5 pb-10 box-border">
          <div className="slogon text-xl font-bold text-black leading-6 mt-8">
            {mobileEnglishContent.hero.slogan}
          </div>
          <h1 className="site-title text-sm font-bold text-black leading-5 mt-4">
            {mobileEnglishContent.hero.title}
          </h1>
          <div className="desc mt-6 text-xs font-light text-black leading-[17px] relative">
            <div className="desc-text w-[172px]">
              {mobileEnglishContent.hero.description}
            </div>
            <div className="image w-[187px] absolute -right-2 bottom-4">
              <figure className="w-[187px]">
                <Image
                  src="/images/index/globle-min.webp"
                  alt="Global Service"
                  width={187}
                  height={150}
                  className="w-full block"
                />
              </figure>
            </div>
          </div>
        </div>
      </header>

      {/* Customer Section */}
      <section className="customer max-w-[680px] mx-auto px-5 box-border mt-12">
        <div className="section-title text-center leading-none mb-5">
          <h2 className="text-xl mb-1.5">{mobileEnglishContent.customer.title}</h2>
        </div>
        <CustomerList />
      </section>

      {/* Service Section */}
      <section className="service max-w-[680px] mx-auto mt-14 px-5 box-border">
        <div className="section-title text-center leading-none mb-5">
          <h2 className="text-xl mb-1.5">{mobileEnglishContent.services.title}</h2>
        </div>
        <div className="service-list">
          {mobileEnglishContent.services.items.map((service, index) => {
            const { ref, isVisible } = useScrollShow(100);
            return (
              <div
                key={service.id}
                ref={ref}
                className={`service-item mb-8 transition-all duration-500 ${
                  isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
                }`}
              >
                <div className="figure-area mb-5">
                  <figure className="block">
                    {service.isVideo ? (
                      <video
                        className="w-full h-[212px] bg-transparent rounded-2xl"
                        controls
                        preload="auto"
                        poster="/images/index/video-bg.jpg"
                      >
                        <source src={service.image} type="video/mp4" />
                      </video>
                    ) : (
                      <Image
                        src={service.image}
                        alt={service.alt}
                        width={640}
                        height={360}
                        className="w-full rounded-lg"
                      />
                    )}
                  </figure>
                </div>
                <div className="service-content">
                  <div className="service-title leading-none mb-2.5 text-gray-800">
                    <h3 className="text-xl mb-2">{service.title}</h3>
                  </div>
                  <div className="service-desc text-xs leading-4 text-gray-600">
                    {service.description.split('. ').map((sentence, idx) => (
                      <p key={idx} className="mb-2.5 text-justify">
                        {sentence}{idx < service.description.split('. ').length - 1 ? '.' : ''}
                      </p>
                    ))}
                    <button
                      className="service-contact-button bg-transparent leading-7 text-sm rounded-[25px] px-6 py-2 border border-gray-800 cursor-pointer flex items-center hover:bg-gray-800 hover:text-white transition-all duration-300 mt-4"
                      onClick={() => setShowContactForm(true)}
                    >
                      Request More Information
                      <svg className="inline-arrow ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </section>

      {/* Advantage Section */}
      <section className="advantage max-w-[680px] mx-auto mt-14 px-5 box-border">
        <div className="section-title text-center leading-none mb-5">
          <h2 className="text-xl mb-1.5">{mobileEnglishContent.advantages.title}</h2>
        </div>
        <div className="advantage-list flex flex-wrap">
          {mobileEnglishContent.advantages.items.map((advantage, index) => {
            const { ref, isVisible } = useScrollShow(index * 100);
            return (
              <div
                key={index}
                ref={ref}
                className={`advantage-item w-1/2 h-[130px] flex flex-col items-center justify-center text-center text-gray-800 transition-all duration-500 ${
                  isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
                }`}
              >
                <figure className="advantage-icon-area flex items-center justify-center mb-2">
                  <Image
                    src={advantage.icon}
                    alt={advantage.alt}
                    width={32}
                    height={32}
                    className="w-8 h-8"
                  />
                </figure>
                <div className="advantage-title text-sm font-semibold text-gray-900">
                  {advantage.title}
                </div>
                <div className="advantage-content text-xs mt-1 text-gray-700 px-2">
                  {advantage.description}
                </div>
              </div>
            );
          })}
        </div>
      </section>

      {/* Lifecycle Section */}
      <section className="lifecycle max-w-[680px] mx-auto mt-14 px-5 box-border">
        <div className="section-title text-center leading-none mb-5">
          <h2 className="text-xl mb-1.5">{mobileEnglishContent.lifecycle.title}</h2>
        </div>
        <div className="lifecycle-list">
          <div className="lifecycle-list-container flex justify-between items-center">
            {mobileEnglishContent.lifecycle.items.map((item, index) => {
              const topOffset = [0, 20, 40, 60, 40, 20, 0][lifecycleItems[index]] || 0;
              return (
                <div
                  key={index}
                  className="lifecycle-item relative flex flex-col items-center flex-1 transition-all duration-500"
                  style={{ top: `${topOffset}px` }}
                >
                  <figure className="w-[60px] h-[60px] bg-white rounded-full mb-4 flex items-center justify-center shadow-lg relative">
                    <Image
                      src={item.icon}
                      alt={item.title}
                      width={40}
                      height={40}
                      className="w-10 h-10"
                    />
                    {index < 6 && (
                      <div
                        className="arrow absolute top-[23px] -right-[9px] h-[14px] w-[12px] bg-no-repeat bg-contain z-10"
                        style={{ backgroundImage: 'url("/images/index/arrow.svg")' }}
                      />
                    )}
                  </figure>
                  <div className="title text-center">
                    <h3 className="text-xs font-semibold text-gray-900 leading-tight">
                      {item.title}
                    </h3>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="process max-w-[680px] mx-auto mt-14 px-5 box-border">
        <div className="section-title text-center leading-none mb-5">
          <h2 className="text-xl mb-1.5">{mobileEnglishContent.process.title}</h2>
        </div>
        <div className="process-list">
          {mobileEnglishContent.process.items.map((item, index) => {
            const { ref, isVisible } = useScrollShow(index * 100);
            return (
              <div
                key={index}
                ref={ref}
                className={`process-item mb-8 transition-all duration-500 ${
                  isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
                }`}
              >
                <div className="process-head mb-5">
                  <div className={`process-head-wrapper flex ${index % 2 === 1 ? 'flex-row-reverse' : ''} items-center`}>
                    <figure className="flex-1">
                      <Image
                        src={item.image}
                        alt={item.alt}
                        width={320}
                        height={180}
                        className="w-full rounded-lg"
                      />
                    </figure>
                    <div className={`num text-4xl font-bold text-orange-500 ${index % 2 === 1 ? 'mr-4' : 'ml-4'}`}>
                      {item.num}
                    </div>
                  </div>
                </div>
                <div className="process-content">
                  <h3 className="title text-lg font-semibold text-gray-900 mb-2">
                    {item.title}
                  </h3>
                  <div className="desc text-xs leading-4 text-gray-600">
                    {item.description}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </section>

      {/* Solution Cases Section */}
      <section className="solution max-w-[680px] mx-auto mt-14 px-5 box-border">
        <div className="section-title text-center leading-none mb-5">
          <h2 className="text-xl mb-1.5">{mobileEnglishContent.solution.title}</h2>
          <p className="text-xs text-gray-400">{mobileEnglishContent.solution.subtitle}</p>
        </div>
        <div className="solution-list">
          {mobileEnglishContent.solution.cases.map((caseItem, index) => {
            const { ref, isVisible } = useScrollShow(180);
            return (
              <div
                key={caseItem.id}
                ref={ref}
                className={`solution-item overflow-hidden mb-8 transition-all duration-500 ${
                  isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
                }`}
              >
                <div className="solution-wrapper">
                  <div className="solution-figure mb-4">
                    <figure>
                      <Image
                        src={caseItem.image}
                        alt={caseItem.title}
                        width={640}
                        height={200}
                        className="w-full rounded-lg"
                      />
                    </figure>
                  </div>
                  <div className="solution-content">
                    <div className="solution-title mb-4">
                      <h3 className="text-lg font-bold text-gray-900 mb-2 leading-tight">
                        {caseItem.title}
                      </h3>
                    </div>
                    <div
                      className="solution-desc text-gray-700 text-xs leading-4"
                      style={getSolutionDescStyle(caseItem.id)}
                    >
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-bold text-gray-900 mb-1 text-sm">Background & Challenges</h4>
                          <p className="mb-2">{caseItem.background}</p>
                          <ul className="list-disc list-inside space-y-1">
                            {caseItem.challenges.map((challenge: string, idx: number) => (
                              <li key={idx}>{challenge}</li>
                            ))}
                          </ul>
                          {caseItem.keyProblems && (
                            <div className="mt-2">
                              <p className="font-semibold mb-1">Key challenges faced include:</p>
                              <ul className="list-disc list-inside space-y-1">
                                {caseItem.keyProblems.map((problem: string, idx: number) => (
                                  <li key={idx}>{problem}</li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </div>

                        <div>
                          <h4 className="font-bold text-gray-900 mb-1 text-sm">Solutions Provided by SmartDeer</h4>
                          <div className="space-y-2">
                            {caseItem.solutions.map((solution, idx) => (
                              <div key={idx}>
                                <p className="font-semibold">{solution.title}</p>
                                <ul className="list-disc list-inside space-y-1 ml-3">
                                  {solution.items.map((item: string, itemIdx: number) => (
                                    <li key={itemIdx}>{item}</li>
                                  ))}
                                </ul>
                              </div>
                            ))}
                          </div>
                        </div>

                        <div>
                          <h4 className="font-bold text-gray-900 mb-1 text-sm">Results Achieved</h4>
                          <ul className="list-disc list-inside space-y-1">
                            {caseItem.results.map((result: string, idx: number) => (
                              <li key={idx}>{result}</li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-bold text-gray-900 mb-1 text-sm">Client Testimonial</h4>
                          <p className="italic text-gray-600">{caseItem.testimonial}</p>
                        </div>
                      </div>
                    </div>
                    <div className="solution-expand mt-3">
                      <button
                        onClick={() => toggleSolution(caseItem.id)}
                        className="solution-toggle inline-flex items-center text-orange-500 font-medium hover:text-orange-600 transition-colors text-sm"
                      >
                        <span>{!solutionExpandStatus[caseItem.id] ? 'Expand' : 'Collapse'}</span>
                        <svg className="inline-arrow ml-1 w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d={!solutionExpandStatus[caseItem.id] ? "M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" : "M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"} clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </section>

      {/* Contact Form */}
      {showContactForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <ContactForm
              onClose={() => setShowContactForm(false)}
              onSubmit={submitSuccess}
            />
          </div>
        </div>
      )}

      {/* Consultant Code */}
      <div className="anchor fixed right-5 bottom-[186px] z-[99]">
        <div
          className="consultant w-[73px] h-[81px] cursor-pointer"
          onClick={() => setShowConsultantCode(!showConsultantCode)}
        >
          <figure className="w-full h-full">
            <Image
              src="/images/index/anchor-avatar-en.png"
              alt="Consultant"
              width={73}
              height={81}
              className="block w-full h-full"
            />
          </figure>
        </div>
        {showConsultantCode && (
          <div className="consultant-code w-[236px] h-[321px] fixed right-[83px] bottom-[169px]">
            <div
              className="close w-5 h-5 cursor-pointer absolute top-[27px] right-[35px]"
              onClick={() => setShowConsultantCode(false)}
            />
            <figure className="w-full h-full">
              <Image
                src="/images/index/anchor-code-en.png"
                alt="Consultant Code"
                width={236}
                height={321}
                className="block w-full h-full"
              />
            </figure>
          </div>
        )}
      </div>

      {/* Bot Button */}
      <div
        className="bot-container fixed right-5 bottom-[100px] z-[99] cursor-pointer"
        onClick={toggleChat}
      >
        <Image
          src="/images/index/bot_logo_en.png"
          alt="Chat Bot"
          width={60}
          height={60}
        />
      </div>

      {/* Go Top Button */}
      <div
        className="go-top-container fixed right-5 bottom-[30px] z-[99] cursor-pointer"
        onClick={() => smoothScrollTo(500, 0)}
      >
        <Image
          src="/images/index/top_icon.png"
          alt="Go Top"
          width={40}
          height={40}
        />
      </div>
    </div>
  );
}
