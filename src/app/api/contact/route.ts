import { NextRequest, NextResponse } from 'next/server';
import { ContactFormData } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, email, company, message, locale, timestamp } = body;

    // 基本验证
    if (!name || !email || !message) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // 邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // 这里应该集成实际的邮件服务或 CRM 系统
    // 例如：SendGrid, AWS SES, 或者企业内部的 CRM 系统
    
    // 模拟处理联系表单数据
    const contactData: ContactFormData & { locale: string; timestamp: string } = {
      name,
      email,
      company: company || '',
      message,
      locale,
      timestamp
    };

    // 在实际应用中，这里应该：
    // 1. 发送邮件通知给销售团队
    // 2. 将数据保存到数据库
    // 3. 集成到 CRM 系统
    // 4. 发送自动回复邮件给用户

    console.log('Contact form submission:', contactData);

    // 模拟异步处理
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 根据语言返回不同的成功消息
    const successMessages = {
      zh: '感谢您的咨询！我们会在24小时内与您联系。',
      en: 'Thank you for your inquiry! We will contact you within 24 hours.',
      ja: 'お問い合わせありがとうございます！24時間以内にご連絡いたします。'
    };

    return NextResponse.json({
      success: true,
      message: successMessages[locale as keyof typeof successMessages] || successMessages.zh
    });

  } catch (error) {
    console.error('Contact form error:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// 处理 OPTIONS 请求（CORS 预检）
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}