import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import '@/styles/globals.css';
import '@/styles/animations.css';
import { generateMetadata as generateSeoMetadata } from '@/utils/seo';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = generateSeoMetadata({
  title: 'SmartDeer - 全球招聘雇佣，海外人力资源一站式服务平台',
  description: 'SmartDeer提供全球招聘、海外雇佣、薪酬管理等一站式人力资源服务，覆盖150+国家，助力企业全球化扩张。专业的EOR、PEO、全球薪酬解决方案。'
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <head>
        {/* 百度验证 */}
        <meta name="baidu-site-verification" content="codeva-DuTIOCkSwLU370iL" />
        
        {/* DNS 预解析 */}
        <link rel="dns-prefetch" href="//hm.baidu.com" />
        <link rel="dns-prefetch" href="//www.googletagmanager.com" />
        <link rel="dns-prefetch" href="//bat.bing.com" />
        
        {/* 预连接重要资源 */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        
        {/* 结构化数据 */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'Organization',
              name: 'SmartDeer',
              description: 'SmartDeer提供全球招聘、海外雇佣、薪酬管理等一站式人力资源服务，覆盖150+国家，助力企业全球化扩张。',
              url: 'https://www.smartdeer.work',
              logo: 'https://www.smartdeer.work/images/tg_banner.png',
              contactPoint: {
                '@type': 'ContactPoint',
                email: '<EMAIL>',
                contactType: 'customer service'
              },
              sameAs: [
                'https://twitter.com/SmartDeerGlobal',
                'https://t.me/SmartDeerGlobal',
                'https://www.linkedin.com/company/smartdeer-global'
              ]
            })
          }}
        />
      </head>
      <body className={inter.className}>
        {children}
      </body>
    </html>
  );
}