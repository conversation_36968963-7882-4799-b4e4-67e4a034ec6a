'use client';

import React, { useEffect, useRef } from 'react';
import videojs from 'video.js';
import 'video.js/dist/video-js.css';

interface VideoPlayerProps {
  src: string;
  poster?: string;
  width?: number;
  height?: number;
  autoplay?: boolean;
  controls?: boolean;
  muted?: boolean;
  loop?: boolean;
  preload?: 'auto' | 'metadata' | 'none';
  className?: string;
  onReady?: (player: ReturnType<typeof videojs>) => void;
  onPlay?: () => void;
  onPause?: () => void;
  onEnded?: () => void;
}

export default function VideoPlayer({
  src,
  poster,
  width = 640,
  height = 360,
  autoplay = false,
  controls = true,
  muted = false,
  loop = false,
  preload = 'auto',
  className = '',
  onReady,
  onPlay,
  onPause,
  onEnded
}: VideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<ReturnType<typeof videojs> | null>(null);

  useEffect(() => {
    // 确保只在客户端初始化
    if (!videoRef.current) return;

    // 初始化 video.js
    const player = videojs(videoRef.current, {
      controls: controls,
      autoplay: autoplay,
      muted: muted,
      loop: loop,
      preload: preload,
      width: width,
      height: height,
      poster: poster,
      sources: [
        {
          src: src,
          type: getVideoType(src)
        }
      ],
      // 响应式设计
      responsive: true,
      fluid: true,
      // 播放器选项
      playbackRates: [0.5, 1, 1.25, 1.5, 2],
      // 语言设置
      language: 'zh-CN',
      // 错误处理
      errorDisplay: true,
      // 自定义样式
      techOrder: ['html5'],
      html5: {
        vhs: {
          overrideNative: true
        }
      }
    });

    playerRef.current = player;

    // 事件监听
    player.ready(() => {
      console.log('Video player is ready');
      onReady?.(player);
    });

    if (onPlay) {
      player.on('play', onPlay);
    }

    if (onPause) {
      player.on('pause', onPause);
    }

    if (onEnded) {
      player.on('ended', onEnded);
    }

    // 错误处理
    player.on('error', () => {
      console.error('Video player error occurred');
    });

    // 清理函数
    return () => {
      if (playerRef.current) {
        playerRef.current.dispose();
        playerRef.current = null;
      }
    };
  }, [src, poster, width, height, autoplay, controls, muted, loop, preload, onReady, onPlay, onPause, onEnded]);

  // 获取视频类型
  const getVideoType = (url: string): string => {
    const extension = url.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'mp4':
        return 'video/mp4';
      case 'webm':
        return 'video/webm';
      case 'ogg':
        return 'video/ogg';
      case 'm3u8':
        return 'application/x-mpegURL';
      default:
        return 'video/mp4';
    }
  };

  return (
    <div className={`video-player-container ${className}`}>
      <div data-vjs-player>
        <video
          ref={videoRef}
          className="video-js vjs-default-skin"
          data-setup="{}"
        />
      </div>
    </div>
  );
}

// 简化版本的视频播放器组件
export function SimpleVideoPlayer({ 
  src, 
  className = '',
  ...props 
}: Omit<VideoPlayerProps, 'onReady' | 'onPlay' | 'onPause' | 'onEnded'>) {
  return (
    <VideoPlayer
      src={src}
      className={className}
      controls={true}
      preload="auto"
      {...props}
    />
  );
}

// 用于服务展示的视频播放器
export function ServiceVideoPlayer({ 
  src, 
  className = 'w-full h-auto rounded-lg shadow-lg' 
}: { 
  src: string; 
  className?: string; 
}) {
  return (
    <SimpleVideoPlayer
      src={src}
      className={className}
      controls={true}
      preload="metadata"
      muted={true}
    />
  );
}
