'use client';

import React from 'react';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  color?: 'primary' | 'secondary' | 'white';
  className?: string;
}

export default function LoadingSpinner({
  size = 'medium',
  color = 'primary',
  className = ''
}: LoadingSpinnerProps) {
  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'w-4 h-4';
      case 'medium':
        return 'w-8 h-8';
      case 'large':
        return 'w-12 h-12';
      default:
        return 'w-8 h-8';
    }
  };

  const getColorClasses = () => {
    switch (color) {
      case 'primary':
        return 'border-orange-500';
      case 'secondary':
        return 'border-blue-500';
      case 'white':
        return 'border-white';
      default:
        return 'border-orange-500';
    }
  };

  return (
    <div
      className={`${getSizeClasses()} border-2 border-t-transparent ${getColorClasses()} rounded-full animate-spin ${className}`}
    />
  );
}

// 页面加载动画
export function PageLoader() {
  return (
    <div className="fixed inset-0 bg-white z-50 flex items-center justify-center">
      <div className="text-center">
        <div className="mb-4">
          <LoadingSpinner size="large" />
        </div>
        <p className="text-gray-600 text-lg">Loading...</p>
      </div>
    </div>
  );
}

// 按钮加载状态
interface LoadingButtonProps {
  children: React.ReactNode;
  loading?: boolean;
  disabled?: boolean;
  onClick?: () => void;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
}

export function LoadingButton({
  children,
  loading = false,
  disabled = false,
  onClick,
  className = '',
  type = 'button'
}: LoadingButtonProps) {
  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || loading}
      className={`relative inline-flex items-center justify-center ${className} ${
        (disabled || loading) ? 'opacity-50 cursor-not-allowed' : ''
      }`}
    >
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <LoadingSpinner size="small" color="white" />
        </div>
      )}
      <span className={loading ? 'opacity-0' : 'opacity-100'}>
        {children}
      </span>
    </button>
  );
}

// 骨架屏组件
interface SkeletonProps {
  width?: string;
  height?: string;
  className?: string;
  rounded?: boolean;
}

export function Skeleton({
  width = 'w-full',
  height = 'h-4',
  className = '',
  rounded = false
}: SkeletonProps) {
  return (
    <div
      className={`${width} ${height} bg-gray-200 animate-pulse ${
        rounded ? 'rounded-full' : 'rounded'
      } ${className}`}
    />
  );
}

// 卡片骨架屏
export function CardSkeleton() {
  return (
    <div className="bg-white rounded-lg shadow-lg p-6 space-y-4">
      <Skeleton height="h-48" rounded={false} />
      <div className="space-y-2">
        <Skeleton height="h-6" width="w-3/4" />
        <Skeleton height="h-4" width="w-full" />
        <Skeleton height="h-4" width="w-5/6" />
      </div>
      <div className="flex justify-between items-center">
        <Skeleton height="h-4" width="w-1/4" />
        <Skeleton height="h-8" width="w-20" rounded={true} />
      </div>
    </div>
  );
}

// 文章列表骨架屏
export function ArticleListSkeleton() {
  return (
    <div className="space-y-8">
      {[1, 2, 3, 4, 5, 6].map((item) => (
        <div key={item} className="flex gap-6">
          <Skeleton width="w-48" height="h-32" />
          <div className="flex-1 space-y-3">
            <Skeleton height="h-6" width="w-3/4" />
            <Skeleton height="h-4" width="w-full" />
            <Skeleton height="h-4" width="w-5/6" />
            <div className="flex gap-4">
              <Skeleton height="h-4" width="w-20" />
              <Skeleton height="h-4" width="w-16" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

// 脉冲动画组件
export function PulseAnimation({
  children,
  className = ''
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <div className={`animate-pulse ${className}`}>
      {children}
    </div>
  );
}

// 波纹动画组件
interface RippleProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

export function Ripple({ children, className = '', onClick }: RippleProps) {
  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const button = e.currentTarget;
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = e.clientX - rect.left - size / 2;
    const y = e.clientY - rect.top - size / 2;
    
    const ripple = document.createElement('span');
    ripple.style.cssText = `
      position: absolute;
      width: ${size}px;
      height: ${size}px;
      left: ${x}px;
      top: ${y}px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      transform: scale(0);
      animation: ripple 0.6s linear;
      pointer-events: none;
    `;
    
    button.appendChild(ripple);
    
    setTimeout(() => {
      ripple.remove();
    }, 600);
    
    onClick?.();
  };

  return (
    <div
      className={`relative overflow-hidden cursor-pointer ${className}`}
      onClick={handleClick}
      style={{
        WebkitTapHighlightColor: 'transparent'
      }}
    >
      {children}
      <style jsx>{`
        @keyframes ripple {
          to {
            transform: scale(4);
            opacity: 0;
          }
        }
      `}</style>
    </div>
  );
}

// 进度条组件
interface ProgressBarProps {
  progress: number;
  className?: string;
  showPercentage?: boolean;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
}

export function ProgressBar({
  progress,
  className = '',
  showPercentage = false,
  color = 'primary'
}: ProgressBarProps) {
  const getColorClasses = () => {
    switch (color) {
      case 'primary':
        return 'bg-orange-500';
      case 'secondary':
        return 'bg-blue-500';
      case 'success':
        return 'bg-green-500';
      case 'warning':
        return 'bg-yellow-500';
      case 'danger':
        return 'bg-red-500';
      default:
        return 'bg-orange-500';
    }
  };

  return (
    <div className={`w-full ${className}`}>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className={`h-2 rounded-full transition-all duration-300 ease-out ${getColorClasses()}`}
          style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
        />
      </div>
      {showPercentage && (
        <div className="text-sm text-gray-600 mt-1 text-right">
          {Math.round(progress)}%
        </div>
      )}
    </div>
  );
}

// 加载点动画
export function LoadingDots({
  size = 'medium',
  color = 'primary',
  className = ''
}: {
  size?: 'small' | 'medium' | 'large';
  color?: 'primary' | 'secondary' | 'white';
  className?: string;
}) {
  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'w-1 h-1';
      case 'medium':
        return 'w-2 h-2';
      case 'large':
        return 'w-3 h-3';
      default:
        return 'w-2 h-2';
    }
  };

  const getColorClasses = () => {
    switch (color) {
      case 'primary':
        return 'bg-orange-500';
      case 'secondary':
        return 'bg-blue-500';
      case 'white':
        return 'bg-white';
      default:
        return 'bg-orange-500';
    }
  };

  return (
    <div className={`flex space-x-1 ${className}`}>
      {[0, 1, 2].map((index) => (
        <div
          key={index}
          className={`${getSizeClasses()} ${getColorClasses()} rounded-full animate-pulse`}
          style={{
            animationDelay: `${index * 0.2}s`,
            animationDuration: '1s'
          }}
        />
      ))}
    </div>
  );
}
