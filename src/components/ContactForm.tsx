'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Locale, ContactFormData } from '@/types';

interface ContactFormProps {
  locale: Locale;
  onClose?: () => void;
  onSubmit?: (data: ContactFormData) => void;
}

const ContactForm: React.FC<ContactFormProps> = ({ locale, onClose, onSubmit: onSubmitProp }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<ContactFormData>();

  const getContent = () => {
    const content = {
      zh: {
        title: '联系我们',
        name: '姓名',
        email: '邮箱',
        company: '公司名称',
        message: '留言',
        submit: '提交',
        submitting: '提交中...',
        success: '提交成功！我们会尽快与您联系。',
        error: '提交失败，请稍后重试。',
        required: '此字段为必填项',
        invalidEmail: '请输入有效的邮箱地址',
        namePlaceholder: '请输入您的姓名',
        emailPlaceholder: '请输入您的邮箱',
        companyPlaceholder: '请输入您的公司名称（可选）',
        messagePlaceholder: '请描述您的需求或问题'
      },
      en: {
        title: 'Contact Us',
        name: 'Name',
        email: 'Email',
        company: 'Company',
        message: 'Message',
        submit: 'Submit',
        submitting: 'Submitting...',
        success: 'Submitted successfully! We will contact you soon.',
        error: 'Submission failed, please try again later.',
        required: 'This field is required',
        invalidEmail: 'Please enter a valid email address',
        namePlaceholder: 'Enter your name',
        emailPlaceholder: 'Enter your email',
        companyPlaceholder: 'Enter your company name (optional)',
        messagePlaceholder: 'Describe your needs or questions'
      },
      ja: {
        title: 'お問い合わせ',
        name: 'お名前',
        email: 'メールアドレス',
        company: '会社名',
        message: 'メッセージ',
        submit: '送信',
        submitting: '送信中...',
        success: '送信が完了しました！お早めにご連絡いたします。',
        error: '送信に失敗しました。後でもう一度お試しください。',
        required: 'この項目は必須です',
        invalidEmail: '有効なメールアドレスを入力してください',
        namePlaceholder: 'お名前を入力してください',
        emailPlaceholder: 'メールアドレスを入力してください',
        companyPlaceholder: '会社名を入力してください（任意）',
        messagePlaceholder: 'ご要望やご質問をお聞かせください'
      }
    };
    
    return content[locale] || content.zh;
  };

  const content = getContent();

  const onSubmit = async (data: ContactFormData) => {
    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      // 调用传入的 onSubmit 回调或默认处理
      if (onSubmitProp) {
        await onSubmitProp(data);
      } else {
        // 默认的 API 调用
        const response = await fetch('/api/contact', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...data,
            locale,
            timestamp: new Date().toISOString()
          }),
        });

        if (!response.ok) {
          throw new Error('Submission failed');
        }
      }

      setSubmitStatus('success');
      reset();
      setTimeout(() => {
        onClose?.();
        setSubmitStatus('idle');
      }, 2000);
    } catch (error) {
      console.error('Contact form submission error:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-md p-6 relative max-h-[90vh] overflow-y-auto">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* Title */}
        <h2 className="text-2xl font-bold text-gray-800 mb-6 pr-8">
          {content.title}
        </h2>

        {/* Success/Error Messages */}
        {submitStatus === 'success' && (
          <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
            {content.success}
          </div>
        )}

        {submitStatus === 'error' && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {content.error}
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Name Field */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              {content.name} *
            </label>
            <input
              type="text"
              id="name"
              {...register('name', { required: content.required })}
              placeholder={content.namePlaceholder}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          {/* Email Field */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              {content.email} *
            </label>
            <input
              type="email"
              id="email"
              {...register('email', {
                required: content.required,
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: content.invalidEmail
                }
              })}
              placeholder={content.emailPlaceholder}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            />
            {errors.email && (
              <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
            )}
          </div>

          {/* Company Field */}
          <div>
            <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-1">
              {content.company}
            </label>
            <input
              type="text"
              id="company"
              {...register('company')}
              placeholder={content.companyPlaceholder}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            />
          </div>

          {/* Message Field */}
          <div>
            <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
              {content.message} *
            </label>
            <textarea
              id="message"
              rows={4}
              {...register('message', { required: content.required })}
              placeholder={content.messagePlaceholder}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-vertical"
            />
            {errors.message && (
              <p className="mt-1 text-sm text-red-600">{errors.message.message}</p>
            )}
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-orange-500 text-white py-2 px-4 rounded-md hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isSubmitting ? content.submitting : content.submit}
          </button>
        </form>
      </div>
    </div>
  );
};

export default ContactForm;