import { SiteConfig } from '@/types';

const env = process.env.NODE_ENV || 'development';

export const CONFIG: Record<string, SiteConfig> = {
  test: {
    mobile_site_host: 'https://m-test.smartdeer.work',
    pc_site_host: 'https://www-test.smartdeer.work'
  },
  production: {
    mobile_site_host: 'https://m.smartdeer.work',
    pc_site_host: 'https://www.smartdeer.work'
  },
  development: {
    mobile_site_host: '',
    pc_site_host: ''
  }
};

export const SITE_CONFIG = CONFIG[env] || CONFIG.development;

export const LOCALES = ['zh', 'en', 'ja'] as const;
export const DEFAULT_LOCALE = 'zh';

export const SEO_CONFIG = {
  defaultTitle: 'SmartDeer - 全球招聘雇佣，海外人力资源一站式服务平台',
  titleTemplate: '%s | SmartDeer',
  defaultDescription: 'SmartDeer提供全球招聘、海外雇佣、薪酬管理等一站式人力资源服务，覆盖150+国家，助力企业全球化扩张。专业的EOR、PEO、全球薪酬解决方案。',
  defaultKeywords: '全球招聘,海外雇佣,人力资源外包,EOR,PEO,全球薪酬,海外用工,国际招聘,SmartDeer,人力资源服务,全球化扩张,跨境用工,海外人事,国际人才',
  siteUrl: SITE_CONFIG.pc_site_host || 'https://www.smartdeer.work',
  ogImage: 'https://www.smartdeer.work/images/tg_banner.png'
};