'use client';

import { useEffect, useRef } from 'react';

interface ScrollAnimationOptions {
  delayOffset?: number;
  threshold?: number;
  rootMargin?: string;
}



export function useScrollAnimation(options: ScrollAnimationOptions = {}) {
  const observerRef = useRef<IntersectionObserver | null>(null);

  const {
    delayOffset = 0,
    threshold = 0.1,
    rootMargin = '0px 0px -50px 0px'
  } = options;

  useEffect(() => {
    // 创建 Intersection Observer
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const element = entry.target as HTMLElement;
          const itemDelayOffset = parseInt(element.dataset.delayOffset || '0');
          
          if (entry.isIntersecting) {
            // 添加延迟后显示动画
            setTimeout(() => {
              element.setAttribute('data-scroll-show', 'true');
              element.style.opacity = '1';
              element.style.transform = 'translateY(0)';
            }, itemDelayOffset);
          }
        });
      },
      {
        threshold,
        rootMargin
      }
    );

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [threshold, rootMargin]);

  const registerElement = (element: HTMLElement, elementDelayOffset: number = delayOffset) => {
    if (!element || !observerRef.current) return;

    // 设置初始样式
    element.style.opacity = '0';
    element.style.transform = 'translateY(30px)';
    element.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
    element.dataset.delayOffset = elementDelayOffset.toString();

    // 开始观察元素
    observerRef.current.observe(element);
  };

  const unregisterElement = (element: HTMLElement) => {
    if (!element || !observerRef.current) return;
    observerRef.current.unobserve(element);
  };

  return {
    registerElement,
    unregisterElement
  };
}

// 全局滚动监听器
let isScrollListenerAdded = false;
const scrollElements: Array<{
  element: HTMLElement;
  offsetTop: number;
  animated: boolean;
}> = [];

function handleScroll() {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const windowHeight = window.innerHeight;

  scrollElements.forEach((item) => {
    if (!item.animated && scrollTop + windowHeight > item.offsetTop + 100) {
      item.animated = true;
      item.element.style.opacity = '1';
      item.element.style.transform = 'translateY(0) translateX(0) scale(1)';
      item.element.classList.add('animate-in');
    }
  });
}

// React Hook for easier usage in components
export function useScrollShow(delayOffset: number = 0) {
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // 添加到滚动元素数组
    const scrollItem = {
      element,
      offsetTop: element.offsetTop + delayOffset,
      animated: false
    };
    scrollElements.push(scrollItem);

    // 添加全局滚动监听器（只添加一次）
    if (!isScrollListenerAdded) {
      window.addEventListener('scroll', handleScroll, { passive: true });
      isScrollListenerAdded = true;
    }

    // 立即检查是否在视口内
    handleScroll();

    return () => {
      // 从数组中移除
      const index = scrollElements.findIndex(item => item.element === element);
      if (index > -1) {
        scrollElements.splice(index, 1);
      }
    };
  }, [delayOffset]);

  return elementRef;
}

// Higher-order component for scroll animation
// Note: This function should be used in .tsx files where JSX is supported
export function withScrollAnimation<T extends object>(
  Component: React.ComponentType<T>,
  delayOffset: number = 0
) {
  return function ScrollAnimatedComponent(props: T) {
    const ref = useScrollShow(delayOffset);

    // Return the ref and component for manual JSX composition
    return { ref, Component, props };
  };
}
