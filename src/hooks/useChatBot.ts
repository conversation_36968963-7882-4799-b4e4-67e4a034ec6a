'use client';

import { useEffect, useRef, useState } from 'react';

interface ChatBotOptions {
  botId: string;
  title?: string;
  icon?: string;
  layout?: 'pc' | 'mobile';
  zIndex?: number;
  uploadable?: boolean;
  footerText?: string;
}

interface ChatBotConfig {
  config: {
    botId: string;
  };
  ui: {
    base: {
      icon?: string;
      layout?: 'pc' | 'mobile';
      zIndex?: number;
    };
    chatBot: {
      title?: string;
      uploadable?: boolean;
    };
    asstBtn: {
      isNeed: boolean;
    };
    footer: {
      isShow: boolean;
      expressionText?: string;
    };
  };
}

interface ChatBotInstance {
  showChatBot: () => void;
  hideChatBot: () => void;
  destroy: () => void;
}

declare global {
  interface Window {
    CozeWebSDK?: {
      WebChatClient: new (config: ChatBotConfig) => ChatBotInstance;
    };
  }
}

export function useChatBot(options: ChatBotOptions) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const chatBotRef = useRef<ChatBotInstance | null>(null);
  const scriptLoadedRef = useRef(false);

  const {
    botId,
    title = '智能助手',
    icon = '/images/index/bot_logo_zh.png',
    layout = 'pc',
    zIndex = 1000,
    uploadable = false,
    footerText = 'Powered by SmartDeer.'
  } = options;

  // 加载 CozeWebSDK 脚本
  useEffect(() => {
    if (scriptLoadedRef.current) return;

    const loadScript = () => {
      return new Promise<void>((resolve, reject) => {
        // 检查是否已经存在
        if (window.CozeWebSDK) {
          resolve();
          return;
        }

        const script = document.createElement('script');
        script.src = 'https://lf-cdn.coze.cn/obj/unpkg/flow-platform/chat-app-sdk/0.1.0-beta.4/libs/oversea/index.js';
        script.async = true;
        script.onload = () => resolve();
        script.onerror = () => reject(new Error('Failed to load CozeWebSDK'));
        document.head.appendChild(script);
      });
    };

    loadScript()
      .then(() => {
        scriptLoadedRef.current = true;
        setIsLoaded(true);
      })
      .catch((error) => {
        console.error('Failed to load chat bot SDK:', error);
        // 如果加载失败，使用备用方案
        setIsLoaded(true);
      });
  }, []);

  // 初始化聊天机器人
  useEffect(() => {
    if (!isLoaded || !botId) return;

    try {
      if (window.CozeWebSDK) {
        chatBotRef.current = new window.CozeWebSDK.WebChatClient({
          config: {
            botId: botId
          },
          ui: {
            base: {
              icon: icon,
              layout: layout,
              zIndex: zIndex
            },
            chatBot: {
              title: title,
              uploadable: uploadable,
            },
            asstBtn: {
              isNeed: false
            },
            footer: {
              isShow: true,
              expressionText: footerText
            }
          }
        });
      }
    } catch (error) {
      console.error('Failed to initialize chat bot:', error);
    }

    return () => {
      if (chatBotRef.current) {
        try {
          chatBotRef.current.destroy();
        } catch (error) {
          console.error('Failed to destroy chat bot:', error);
        }
      }
    };
  }, [isLoaded, botId, title, icon, layout, zIndex, uploadable, footerText]);

  const showChatBot = () => {
    if (chatBotRef.current) {
      try {
        chatBotRef.current.showChatBot();
        setIsVisible(true);
      } catch (error) {
        console.error('Failed to show chat bot:', error);
        // 备用方案：显示一个简单的提示
        alert('聊天功能暂时不可用，请稍后再试或联系客服。');
      }
    } else {
      // 备用方案：显示联系信息
      alert('聊天功能正在加载中，请稍后再试或发送邮件至 <EMAIL>');
    }
  };

  const hideChatBot = () => {
    if (chatBotRef.current) {
      try {
        chatBotRef.current.hideChatBot();
        setIsVisible(false);
      } catch (error) {
        console.error('Failed to hide chat bot:', error);
      }
    }
  };

  const toggleChatBot = () => {
    if (isVisible) {
      hideChatBot();
    } else {
      showChatBot();
    }
  };

  return {
    isLoaded,
    isVisible,
    showChatBot,
    hideChatBot,
    toggleChatBot
  };
}

// 简化的聊天机器人Hook，用于快速集成
export function useSimpleChatBot(botId: string) {
  return useChatBot({
    botId,
    title: '智能助手',
    icon: '/images/index/bot_logo_zh.png',
    layout: 'pc',
    uploadable: false,
    footerText: 'Powered by SmartDeer.'
  });
}
