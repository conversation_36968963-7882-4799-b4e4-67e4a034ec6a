export type Locale = 'zh' | 'en' | 'ja';

export interface SiteConfig {
  mobile_site_host: string;
  pc_site_host: string;
}

export interface NavigationItem {
  label: string;
  href?: string;
  onClick?: () => void;
  children?: NavigationItem[];
}

export interface SeoMeta {
  title: string;
  description: string;
  keywords: string;
  ogImage?: string;
  canonical?: string;
}

export interface ContactFormData {
  name: string;
  email: string;
  company?: string;
  message: string;
}

export interface ArticleData {
  id: string;
  title: string;
  description: string;
  content: string;
  slug: string;
  publishedAt: string;
  locale: Locale;
  seo?: SeoMeta;
}