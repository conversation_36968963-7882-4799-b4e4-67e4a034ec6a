/* V1 版本兼容样式 */

/* 重置样式，匹配 v1 版本 */
html, body {
  padding: 0;
  margin: 0;
  font-family: PingFang SC, Hiragino Sans GB, Microsoft YaHei, WenQuanYi Micro Hei, Helvetica Neue, Arial, sans-serif;
  font-smooth: always;
  -webkit-font-smoothing: antialiased;
}

figure {
  margin-block-start: 0;
  margin-block-end: 0;
  margin-inline-start: 0;
  margin-inline-end: 0;
}

img {
  display: block;
}

ul {
  margin-block-start: 0;
  margin-block-end: 0;
  padding-inline-start: 0;
}

h1, h2, h3, h4, h5, h6 {
  margin: 0;
  padding: 0;
  margin-block-start: 0;
  margin-block-end: 0;
}

a, a:active {
  color: #FF8600;
  text-decoration: none;
}

/* 首页样式 */
.index-page {
  min-height: 100vh;
}

/* Header Banner 样式 */
.header-banner {
  background: linear-gradient(135deg, #f0f9ff 0%, #fef3e2 100%);
  padding: 4rem 0 6rem 0;
}

.header-banner-text .slogon {
  color: #FF8600;
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.header-banner-text .title {
  font-size: 3rem;
  font-weight: bold;
  color: #1f2937;
  line-height: 1.2;
  margin-bottom: 1.5rem;
}

.header-banner-text .desc {
  font-size: 1.25rem;
  color: #6b7280;
  line-height: 1.6;
}

.header-banner-image {
  position: relative;
  height: 400px;
}

.header-banner-image figure {
  position: absolute;
}

.header-banner-image .global-min_1 {
  top: 0;
  left: 0;
  width: 200px;
}

.header-banner-image .global-min_2 {
  top: 8rem;
  left: 0;
  width: 200px;
}

.header-banner-image .global-min_3 {
  top: 2rem;
  right: 0;
  width: 200px;
}

/* Section 样式 */
.section-title {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title h2 {
  font-size: 2.5rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 1rem;
}

.section-title p {
  font-size: 1.125rem;
  color: #6b7280;
}

/* Service 样式 */
.service {
  background-color: #f9fafb;
  padding: 4rem 0 6rem 0;
}

.service-item {
  margin-bottom: 6rem;
}

.service-content {
  padding: 2rem 0;
}

.service-title h3 {
  font-size: 2rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.service-title p {
  font-size: 1.125rem;
  color: #6b7280;
  font-weight: 500;
}

.service-desc {
  margin: 1.5rem 0;
}

.service-desc p {
  color: #374151;
  line-height: 1.7;
}

.service-contact-button {
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background-color: #FF8600;
  color: white;
  font-weight: bold;
  border-radius: 9999px;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.service-contact-button:hover {
  background-color: #e67300;
}

.inline-arrow {
  margin-left: 0.5rem;
  width: 1rem;
  height: 1rem;
}

/* Advantages 样式 */
.advantages {
  background-color: white;
  padding: 4rem 0 6rem 0;
}

.advantage-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.advantage-item {
  text-align: center;
  padding: 2rem 1rem;
}

.advantage-icon-area {
  width: 4rem;
  height: 4rem;
  margin: 0 auto 1rem auto;
}

.advantage-title {
  font-size: 1.25rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 1rem;
}

.advantage-content {
  color: #6b7280;
  line-height: 1.6;
}

/* Lifecycle 样式 */
.lifecycle {
  background-color: #f9fafb;
  padding: 4rem 0 6rem 0;
}

.lifecycle-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
}

.lifecycle-item {
  text-align: center;
  position: relative;
}

.lifecycle-item-icon {
  position: relative;
  margin-bottom: 1rem;
}

.lifecycle-item-icon .arrow {
  position: absolute;
  top: 50%;
  right: -1rem;
  transform: translateY(-50%);
  font-size: 1.5rem;
  color: #FF8600;
}

.lifecycle-item-title {
  font-size: 1.125rem;
  font-weight: bold;
  color: #1f2937;
}

.lifecycle-repeat {
  text-align: center;
}

/* Process 样式 */
.process {
  background-color: white;
  padding: 4rem 0 6rem 0;
}

.process-item {
  margin-bottom: 6rem;
  position: relative;
}

.process-background {
  position: relative;
}

.process-num {
  font-size: 4rem;
  font-weight: bold;
  color: #FF8600;
  opacity: 0.2;
}

.process-title h3 {
  font-size: 1.5rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 1rem;
}

.process-desc p {
  color: #374151;
  line-height: 1.7;
}

/* Solution 样式 */
.solution {
  background-color: #f9fafb;
  padding: 4rem 0 6rem 0;
}

.solution-item {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
}

.solution-title h3 {
  font-size: 1.25rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 1rem;
}

.solution-desc {
  color: #6b7280;
  line-height: 1.6;
  overflow: hidden;
  transition: max-height 1s ease-out;
}

.solution-toggle {
  color: #FF8600;
  font-weight: 500;
  cursor: pointer;
  border: none;
  background: none;
  display: inline-flex;
  align-items: center;
  margin-top: 1rem;
}

.solution-toggle:hover {
  color: #e67300;
}

/* Customer List 样式 */
.customer-item {
  width: 12.5%;
  height: 55px;
  margin-bottom: 15px;
  padding: 0 4px;
}

.customer-item figure {
  width: 100%;
  height: 100%;
}

.customer-item img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  user-select: none;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-banner-text .title {
    font-size: 2.5rem;
  }
  
  .header-banner-text .slogon {
    font-size: 1.75rem;
  }
  
  .section-title h2 {
    font-size: 2rem;
  }
  
  .service-title h3 {
    font-size: 1.75rem;
  }
}

@media (max-width: 768px) {
  .header-banner {
    padding: 2rem 0 4rem 0;
  }
  
  .header-banner-text .title {
    font-size: 2rem;
  }
  
  .header-banner-text .slogon {
    font-size: 1.5rem;
  }
  
  .header-banner-image {
    height: 300px;
    margin-top: 2rem;
  }
  
  .section-title h2 {
    font-size: 1.75rem;
  }
  
  .service-title h3 {
    font-size: 1.5rem;
  }
  
  .lifecycle-list {
    flex-direction: column;
    gap: 1rem;
  }
  
  .lifecycle-item-icon .arrow {
    display: none;
  }
  
  .customer-item {
    width: 25%;
  }
}

@media (max-width: 640px) {
  .header-banner-text .title {
    font-size: 1.75rem;
  }
  
  .header-banner-text .desc {
    font-size: 1rem;
  }
  
  .section-title h2 {
    font-size: 1.5rem;
  }
  
  .service-title h3 {
    font-size: 1.25rem;
  }
  
  .advantage-list {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }
  
  .customer-item {
    width: 50%;
  }
}