@tailwind base;
@tailwind components;
@tailwind utilities;

/* Reset styles */
html, body {
  padding: 0;
  margin: 0;
  font-family: PingFang SC, Hiragino Sans GB, Microsoft YaHei, WenQuanYi Micro Hei, Helvetica Neue, Arial, sans-serif;
  font-smooth: always;
  -webkit-font-smoothing: antialiased;
}

figure {
  margin-block-start: 0;
  margin-block-end: 0;
  margin-inline-start: 0;
  margin-inline-end: 0;
}

img {
  display: block;
}

ul {
  margin-block-start: 0;
  margin-block-end: 0;
  padding-inline-start: 0;
}

h1, h2, h3, h4, h5, h6 {
  margin: 0;
  padding: 0;
  margin-block-start: 0;
  margin-block-end: 0;
}

a, a:active {
  color: #FF8600;
  text-decoration: none;
}

/* Responsive design utilities */
@layer utilities {
  .container-responsive {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .text-responsive {
    @apply text-sm sm:text-base lg:text-lg;
  }
  
  .heading-responsive {
    @apply text-xl sm:text-2xl lg:text-3xl xl:text-4xl;
  }
}

/* Mobile-first responsive breakpoints */
@media (max-width: 640px) {
  .site-header {
    padding: 1rem 0;
  }
  
  .header-nav {
    display: none;
  }
  
  .mobile-menu-button {
    display: block;
  }
}

@media (min-width: 641px) {
  .mobile-menu-button {
    display: none;
  }
}