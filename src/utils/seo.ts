import { Metadata } from 'next';
import { SeoMeta, Locale } from '@/types';
import { SEO_CONFIG } from '@/lib/config';

// 生成页面元数据
export function generateMetadata(
  seo: Partial<SeoMeta>,
  locale: Locale = 'zh'
): Metadata {
  const title = seo.title 
    ? `${seo.title} | SmartDeer`
    : SEO_CONFIG.defaultTitle;
  
  const description = seo.description || SEO_CONFIG.defaultDescription;
  const keywords = seo.keywords || SEO_CONFIG.defaultKeywords;
  const canonical = seo.canonical || SEO_CONFIG.siteUrl;
  const ogImage = seo.ogImage || SEO_CONFIG.ogImage;

  return {
    title,
    description,
    keywords,
    authors: [{ name: 'SmartDeer' }],
    robots: 'index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1',
    
    // Open Graph
    openGraph: {
      type: 'website',
      siteName: 'SmartDeer',
      title,
      description,
      url: canonical,
      images: [
        {
          url: ogImage,
          width: 1390,
          height: 781,
          alt: 'SmartDeer - 全球人力资源服务平台'
        }
      ],
      locale: getOpenGraphLocale(locale)
    },
    
    // Twitter Card
    twitter: {
      card: 'summary_large_image',
      site: '@smartdeer',
      creator: '@smartdeer',
      title,
      description,
      images: [ogImage]
    },
    
    // 其他 meta 标签
    other: {
      'baidu-site-verification': 'codeva-DuTIOCkSwLU370iL',
      'applicable-device': 'pc,mobile',
      'renderer': 'webkit',
      'format-detection': 'telephone=no'
    },
    
    // Canonical URL
    alternates: {
      canonical
    }
  };
}

// 获取 Open Graph 语言代码
function getOpenGraphLocale(locale: Locale): string {
  const localeMap = {
    zh: 'zh_CN',
    en: 'en_US',
    ja: 'ja_JP'
  };
  
  return localeMap[locale] || 'zh_CN';
}

// 生成结构化数据
export function generateStructuredData(data: {
  type: 'Organization' | 'WebSite' | 'Article';
  name?: string;
  description?: string;
  url?: string;
  logo?: string;
  author?: string;
  datePublished?: string;
  dateModified?: string;
}) {
  const baseStructuredData = {
    '@context': 'https://schema.org',
    '@type': data.type
  };

  switch (data.type) {
    case 'Organization':
      return {
        ...baseStructuredData,
        name: 'SmartDeer',
        description: SEO_CONFIG.defaultDescription,
        url: SEO_CONFIG.siteUrl,
        logo: SEO_CONFIG.ogImage,
        contactPoint: {
          '@type': 'ContactPoint',
          email: '<EMAIL>',
          contactType: 'customer service'
        },
        sameAs: [
          'https://twitter.com/SmartDeerGlobal',
          'https://t.me/SmartDeerGlobal',
          'https://www.linkedin.com/company/smartdeer-global'
        ]
      };
      
    case 'WebSite':
      return {
        ...baseStructuredData,
        name: 'SmartDeer',
        description: SEO_CONFIG.defaultDescription,
        url: SEO_CONFIG.siteUrl,
        potentialAction: {
          '@type': 'SearchAction',
          target: `${SEO_CONFIG.siteUrl}/search?q={search_term_string}`,
          'query-input': 'required name=search_term_string'
        }
      };
      
    case 'Article':
      return {
        ...baseStructuredData,
        headline: data.name,
        description: data.description,
        url: data.url,
        author: {
          '@type': 'Organization',
          name: data.author || 'SmartDeer'
        },
        publisher: {
          '@type': 'Organization',
          name: 'SmartDeer',
          logo: {
            '@type': 'ImageObject',
            url: SEO_CONFIG.ogImage
          }
        },
        datePublished: data.datePublished,
        dateModified: data.dateModified || data.datePublished
      };
      
    default:
      return baseStructuredData;
  }
}