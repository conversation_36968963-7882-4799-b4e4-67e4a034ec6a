import { Locale } from '@/types';

const LANG_COOKIE_KEY = 'sd_intl_lang';

// 获取浏览器语言
export function getBrowserLanguage(): Locale {
  if (typeof window === 'undefined') return 'zh';
  
  const systemLang = window.navigator.language.substring(0, 2);
  return ['zh', 'en', 'ja'].includes(systemLang) ? systemLang as Locale : 'zh';
}

// 获取存储的语言偏好
export function getStoredLanguage(): Locale | null {
  if (typeof window === 'undefined') return null;
  
  const stored = localStorage.getItem(LANG_COOKIE_KEY);
  return stored as Locale || null;
}

// 设置语言偏好
export function setStoredLanguage(lang: Locale): void {
  if (typeof window === 'undefined') return;
  
  localStorage.setItem(LANG_COOKIE_KEY, lang);
}

// 切换语言 - 移除移动端跳转逻辑，改为响应式设计
export function switchLanguage(lang: Locale, path: string = ''): void {
  setStoredLanguage(lang);
  
  // 构建新的路径
  const currentPath = window.location.pathname;
  const pathWithoutLang = currentPath.replace(/^\/(zh|en|ja)/, '');
  const newPath = `/${lang}${pathWithoutLang}${path}`;
  
  // 使用 Next.js 路由进行导航
  window.location.href = newPath;
}

// 检测当前语言
export function getCurrentLanguage(): Locale {
  if (typeof window === 'undefined') return 'zh';
  
  const pathname = window.location.pathname;
  const langMatch = pathname.match(/^\/(zh|en|ja)/);
  
  if (langMatch) {
    return langMatch[1] as Locale;
  }
  
  return getStoredLanguage() || getBrowserLanguage();
}

// 获取本地化路径
export function getLocalizedPath(path: string, locale: Locale): string {
  // 移除现有的语言前缀
  const cleanPath = path.replace(/^\/(zh|en|ja)/, '');
  return `/${locale}${cleanPath}`;
}

const langUtils = {
  getBrowserLanguage,
  getStoredLanguage,
  setStoredLanguage,
  switchLanguage,
  getCurrentLanguage,
  getLocalizedPath
};

export default langUtils;